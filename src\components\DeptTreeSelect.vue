<template>
  <a-tree-select
    v-bind="$attrs"
    :value="modelValue || undefined"
    show-search
    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
    placeholder="请选择部门"
    allow-clear
    tree-default-expand-all
    :tree-data="treeData"
    tree-node-filter-prop="title"
    @change="onchange"
    v-if="type === 'all'"
  ></a-tree-select>
  <a-select
    v-bind="$attrs"
    :value="modelValue || undefined"
    show-search
    :options="treeData"
    :filter-option="filterOption"
    :field-names="{ label: 'title', value: 'id' }"
    @change="onchange"
    v-else
  ></a-select>
</template>

<script setup>
import { getDepartTree } from '@/views/system/depart/apis'

const { modelValue, type } = defineProps({
  modelValue: { required: true, type: [String, Array] },
  // 选择类型 all=全部，即完整的树形数据 company=公司，只取机构类型为“公司”的数据，即树形数据第一级的列表
  type: { type: String, default: 'all' }
})

const emits = defineEmits(['update:modelValue', 'change'])

const onchange = (value) => {
  emits('update:modelValue', value)
  emits('change', value)
}

const filterOption = (input, option) => {
  return option.title.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const treeData = ref([])

onMounted(async () => {
  const { result } = await getDepartTree()
  treeData.value = result
})
</script>
