<template>
  <header class="h-[64px] bg-white flex items-center justify-between border-b border-solid border-[#d7dae0] px-[24px]">
    <div class="flex items-center">
      <div class="mr-[64px]">logo</div>
      <router-link class="entry-btn" :class="{ active: route.path === '/home' }" to="/home">工作台</router-link>
      <a-popover :arrow="false" overlay-class-name="nav-header-popover" placement="bottomLeft">
        <template #content>
          <router-link to="/projects" class="popover-link">项目</router-link>
          <router-link to="/assets/manage" class="popover-link">资产</router-link>
          <router-link to="/leaseUnit/manage" class="popover-link">租赁单元</router-link>
        </template>
        <div class="entry-btn">资产</div>
      </a-popover>
      <a-popover :arrow="false" overlay-class-name="nav-header-popover" placement="bottomLeft">
        <template #content>
          <router-link to="/customer/manage" class="popover-link">客户</router-link>
          <router-link to="/rentScheme" class="popover-link">招租</router-link>
          <router-link to="/contract/management" class="popover-link">合同</router-link>
        </template>
        <div class="entry-btn">运营</div>
      </a-popover>
      <a-popover :arrow="false" overlay-class-name="nav-header-popover" placement="bottomLeft">
        <template #content>
          <router-link to="/system/menu" class="popover-link">菜单管理</router-link>
          <router-link to="/system/menu" class="popover-link">用户管理</router-link>
          <router-link to="/system/menu" class="popover-link">角色管理</router-link>
          <router-link to="/system/menu" class="popover-link">部门管理</router-link>
          <router-link to="/system/menu" class="popover-link">数据字典</router-link>
        </template>
        <div class="entry-btn">管理</div>
      </a-popover>
    </div>
    <a-dropdown>
      <div class="flex items-center cursor-pointer" @click.prevent>
        <img src="@/assets/imgs/avatar.png" class="w-[32px] h-[32px] rounded-[50%]" />
        <span class="ml-[12px] mr-[4px]">你好，{{ userInfo.realname }}</span>
        <i class="a-icon-arrow-down text-[14px] text-[#cecece]"></i>
      </div>
      <template #overlay>
        <a-menu>
          <a-menu-item @click="toPage('/personalCenter')">基础信息</a-menu-item>
          <a-menu-item @click="handleUpdatePassword">密码修改</a-menu-item>
          <a-menu-item @click="handleLogout">退出登录</a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
    <change-password ref="changePasswordRef"></change-password>
  </header>
</template>

<script setup>
import ChangePassword from '@/components/ChangePassword.vue'
import { useUserStore } from '@/store/modules/user'
import { Modal } from 'ant-design-vue'

const store = useUserStore()
const route = useRoute()
const router = useRouter()
const userInfo = computed(() => store.userInfo)

const toPage = (path) => {
  router.push({ path })
}

// 修改密码
const changePasswordRef = ref()
const handleUpdatePassword = () => {
  changePasswordRef?.value.open()
}

// 退出登录
const handleLogout = () => {
  Modal.confirm({
    title: '系统提示',
    content: '是否确认退出登录？',
    centered: true,
    onOk: () => {
      store.logout()
    }
  })
}
</script>

<style lang="less" scoped>
.entry-btn {
  padding: 0 24px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition:
    color 0.2s,
    background-color 0.2s;
  margin-right: 16px;
  &:hover {
    color: var(--color-primary);
  }
  &.active {
    color: var(--color-primary);
    background-color: rgba(var(--color-primary-rgb), 0.1);
  }
}
</style>

<style lang="less">
.nav-header-popover {
  .ant-popover-inner {
    padding: 0;
    overflow: hidden;
  }
  .popover-link {
    width: 200px;
    height: 40px;
    padding: 0 24px;
    display: flex;
    align-items: center;
    cursor: pointer;
    color: var(--color-main);
    transition:
      color 0.2s,
      background-color 0.2s;
    &:hover {
      color: var(--color-primary);
      background-color: rgba(var(--color-primary-rgb), 0.1);
    }
  }
}
</style>
