import { useDictStore } from '@/store/modules/dict'
import StatusTag from '@/components/StatusTag.vue'
import areaList from '@/json/region.json'

const getDictItem = (value, code) => {
  if (typeof value === 'number') {
    value = String(value)
  }
  if (!value || !code) return ''
  const store = useDictStore()
  if (!store.dict) return ''
  if (!Object.keys(store.dict).length) return ''
  const list = store.dict[code]
  if (!(list && list.length)) return ''
  return list.find((i) => i.value === String(value))
}

export const renderDict = (value, code) => {
  const data = getDictItem(value, code)
  return data ? data.label : ''
}

/**
 * 在a-table中以tag样式渲染字典值
 * @param {String} value 字典值
 * @param {String} code 字典编码
 * @param {String} type tag=经典标签样式 dot=前面有一个圆点的样式
 * @param {String} color 自定义颜色值
 */
export const renderDictTag = (value, code, type, color) => {
  const data = getDictItem(value, code)
  if (!data) return ''
  return h(
    StatusTag,
    {
      dictValue: value,
      dictCode: code,
      color: color || (data && data.color),
      type
    },
    () => data.label
  )
}

export const renderBoolean = (value) => {
  return value ? '是' : '否'
}

/**
 * 在a-table中以tag样式渲染字典值
 * @param {String} code 省市区的code值
 */
export const renderAddressName = (code) => {
  if (!code) return ''
  const codeArr = code.split(',')
  let addressName = ''
  areaList.forEach((item) => {
    // 省
    if (item.value === codeArr[0]) {
      addressName += item.label
      item.children.forEach((item) => {
        // 市
        if (item.value === codeArr[1]) {
          addressName += item.label
          item.children.forEach((item) => {
            // 区
            if (item.value === codeArr[2]) {
              addressName += item.label
            }
          })
        }
      })
    }
  })
  return addressName
}
