<template>
  <a-drawer
    class="common-drawer"
    v-model:open="visible"
    :title="ruleForm.id ? '编辑资产' : '新增资产'"
    placement="right"
    width="1072px"
    destroy-on-close
    @close="handleCancel"
  >
    <div class="progress-wrapper mb-[24px]">
      <div class="progress-item mr-[16px]" v-for="(item, index) in processList" :key="index">
        <span class="item-step" :class="{ active: item.status }">
          <span v-if="item.status === 2" class="a-icon-dagou"></span>
          <span v-else class="step-num flex items-center">{{ index + 1 }}</span>
        </span>
        <span class="item-name" :class="{ active: item.status }">{{ item.name }}</span>
        <span class="item-line" :class="{ active: item.status === 2 }"></span>
      </div>
    </div>

    <a-form
      :model="ruleForm"
      ref="formRef"
      :rules="rules"
      label-align="left"
      :label-col="{ style: { width: '150px' } }"
      autocomplete="off"
    >
      <template v-if="processList[0].status == 1">
        <div class="text-[16px] font-bold mb-[12px] mt-[12px]">产权证封面</div>
        <a-form-item label="产权证封面" name="ownerCover">
          <img-upload v-model="ruleForm.ownerCover"></img-upload>
        </a-form-item>
        <div class="text-[16px] font-bold mb-[12px] mt-[12px]">产权证内页</div>
        <a-form-item label="产权证内页" name="ownerInside">
          <img-upload v-model="ruleForm.ownerInside"></img-upload>
        </a-form-item>
        <div class="text-[16px] font-bold mb-[12px] mt-[12px]">资产实拍照片</div>
        <a-form-item label="资产外观实拍" name="ownerRealImage">
          <img-upload v-model="ruleForm.ownerRealImage"></img-upload>
        </a-form-item>
      </template>

      <template v-if="processList[1].status == 1">
        <div class="text-[16px] font-bold mb-[12px] mt-[12px]">资产基础信息</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item :label-col="{ style: { width: '100px' } }" label="资产名称" name="name">
              <a-input v-model:value="ruleForm.name" placeholder="请输入资产名称" :maxlength="50" allow-clear></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :label-col="{ style: { width: '100px' } }" label="资产类型" name="assetsType">
              <dict-select
                v-model="ruleForm.assetsType"
                placeholder="请选择资产类型"
                code="CT_BAS_AssetsType"
              ></dict-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :label-col="{ style: { width: '100px' } }" label="所属项目" name="wyProject">
              <api-select
                v-model="ruleForm.wyProject"
                :async-fn="() => projectPage({ pageNo: 1, pageSize: 10000 })"
                :field-names="{ label: 'name', value: 'id' }"
                placeholder="请选择所属项目"
              ></api-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :label-col="{ style: { width: '100px' } }" label="地址" name="detailAddress">
              <address-select
                v-model="ruleForm.pcaCode"
                v-model:address="ruleForm.detailAddress"
                @setAddress="setAddress"
              ></address-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :label-col="{ style: { width: '100px' } }" label="租金归集公司" name="collectionCompany">
              <dept-tree-select
                v-model="ruleForm.collectionCompany"
                placeholder="请选择租金归集公司"
              ></dept-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :label-col="{ style: { width: '100px' } }" label="资产权属公司" name="ownerCompany">
              <dept-tree-select v-model="ruleForm.ownerCompany" placeholder="请选择资产权属公司"></dept-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :label-col="{ style: { width: '100px' } }" label="物业管理公司" name="manageCompany">
              <dept-tree-select v-model="ruleForm.manageCompany" placeholder="请选择物业管理公司"></dept-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :label-col="{ style: { width: '100px' } }" label="产权分类" name="treeId">
              <api-tree-select
                v-model="ruleForm.treeId"
                :async-fn="getQueryTreeList"
                placeholder="请选择产权分类"
              ></api-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item :label-col="{ style: { width: '100px' } }" label="备注" name="remark">
              <a-textarea
                v-model:value="ruleForm.remark"
                placeholder="请输入备注"
                :maxlength="500"
                show-count
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>

        <div class="text-[16px] font-bold mb-[12px] mt-[12px]">产权信息</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item :label-col="{ style: { width: '100px' } }" label="产权号" name="ownerNumber">
              <a-input v-model:value="ruleForm.ownerNumber" placeholder="请输入产权号" allow-clear></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :label-col="{ style: { width: '100px' } }" label="权证获得日期" name="warrantsDate">
              <a-date-picker
                v-model:value="ruleForm.warrantsDate"
                picker="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择权证获得日期"
                class="w-[100%]"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :label-col="{ style: { width: '100px' } }" label="取得来源" name="acquisitionMethod">
              <dict-select
                v-model="ruleForm.acquisitionMethod"
                placeholder="请选择取得来源"
                code="CT_BAS_AcquisitionMethod"
              ></dict-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :label-col="{ style: { width: '100px' } }" label="产权情况" name="propertyRightStatus">
              <dict-select
                v-model="ruleForm.propertyRightStatus"
                placeholder="请选择产权情况"
                code="CT_BASE_ENUM_HouseOwner_PropertyRightStatus"
              ></dict-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :label-col="{ style: { width: '100px' } }" label="代管委托方" name="proxies">
              <a-input v-model:value="ruleForm.proxies" placeholder="请输入代管委托方" allow-clear></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :label-col="{ style: { width: '100px' } }" label="房地权证合一" name="isUnionCertificate">
              <a-select v-model:value="ruleForm.isUnionCertificate" placeholder="请选择房地权证合一" allow-clear>
                <a-select-option v-for="item in isOrNotDic" :key="item.dictKey" :value="item.dictKey">
                  {{ item.dictValue }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :label-col="{ style: { width: '100px' } }" label="产权用途" name="propertyUse">
              <dict-select
                v-model="ruleForm.propertyUse"
                placeholder="请选择产权用途"
                code="CT_BAS_PropertyUse"
              ></dict-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item :label-col="{ style: { width: '100px' } }" label="使用权类型" name="landNature">
              <dict-select
                v-model="ruleForm.landNature"
                placeholder="请选择使用权类型"
                code="CT_BAS_LandNature"
              ></dict-select>
            </a-form-item>
          </a-col>
        </a-row>
      </template>
      <template v-if="processList[2].status == 1">
        <div class="text-[16px] font-bold mb-[12px] mt-[12px]">土地信息</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="土地取得价格" name="landPrice">
              <a-input-number
                v-model:value="ruleForm.landPrice"
                addon-after="万元"
                :min="0"
                :precision="2"
                placeholder="请输入土地取得价格"
                class="w-[100%]"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="土地建设情况" name="landConstructionSituation">
              <dict-select
                v-model="ruleForm.landConstructionSituation"
                placeholder="请选择土地建设情况"
                code="CT_BAS_LandCS"
              ></dict-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="租赁土地租金" name="landRent">
              <a-input-number
                v-model:value="ruleForm.landRent"
                addon-after="万元/年"
                :min="0"
                :precision="2"
                class="w-[100%]"
                placeholder="请输入租赁土地租金"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="地价款(租金)欠缴金额" name="arrearsAmount">
              <a-input-number
                v-model:value="ruleForm.arrearsAmount"
                addon-after="万元"
                :min="0"
                :precision="2"
                class="w-[100%]"
                placeholder="请输入地价款(租金)欠缴金额"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>

        <div class="text-[16px] font-bold mb-[12px] mt-[12px]">建筑信息</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="房产类型" name="houseType">
              <dict-select
                v-model="ruleForm.houseType"
                placeholder="请选择房产类型"
                code="CT_BASE_ENUM_HouseOwner_HouseType"
              ></dict-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="建筑面积(㎡)" name="structureArea">
              <a-input-number
                v-model:value="ruleForm.structureArea"
                addon-after="㎡"
                :min="0"
                :precision="2"
                class="w-[100%]"
                placeholder="请输入建筑面积"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="宗地面积(㎡)" name="floorArea">
              <a-input-number
                v-model:value="ruleForm.floorArea"
                addon-after="㎡"
                :min="0"
                :precision="2"
                class="w-[100%]"
                placeholder="请输入宗地面积"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="建筑结构" name="buildStructrue">
              <dict-select
                v-model="ruleForm.buildStructrue"
                placeholder="请选择建筑结构"
                code="CT_BAS_BuildStructrue"
              ></dict-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="建筑年份" name="buildYear">
              <a-date-picker
                class="w-[100%]"
                v-model:value="ruleForm.buildYear"
                picker="year"
                value-format="YYYY"
                format="YYYY"
                placeholder="请选择建筑年份"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="层数" name="layerNum">
              <a-input-number
                v-model:value="ruleForm.layerNum"
                :min="1"
                :precision="0"
                class="!w-[100%]"
                placeholder="请输入层数"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="总层数" name="totalLayerNum">
              <a-input-number
                v-model:value="ruleForm.totalLayerNum"
                :min="1"
                :precision="0"
                class="!w-[100%]"
                placeholder="请输入总层数"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="层高(m)" name="layerHight">
              <a-input-number
                v-model:value="ruleForm.layerHight"
                addon-after="m"
                :min="0"
                :precision="2"
                class="w-[100%]"
                placeholder="请输入层高"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="户型" name="houseModel">
              <a-input v-model:value="ruleForm.houseModel" placeholder="请输入户型" allow-clear></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="消防等级" name="firefightingRate">
              <dict-select
                v-model="ruleForm.firefightingRate"
                placeholder="请选择建筑结构"
                code="CT_BAS_FirefightingRate"
              ></dict-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="房屋安全等级" name="houseSafeRate">
              <dict-select
                v-model="ruleForm.houseSafeRate"
                placeholder="请选择房屋安全等级"
                code="CT_BAS_HouseSafeRate"
              ></dict-select>
            </a-form-item>
          </a-col>
        </a-row>
      </template>
      <template v-if="processList[3].status == 1">
        <div class="text-[16px] font-bold mb-[12px] mt-[12px]">税务信息</div>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="产权比例(%)" name="propertyRate">
              <a-input-number
                v-model:value="ruleForm.propertyRate"
                addon-after="%"
                :min="0"
                :precision="2"
                class="w-[100%]"
                placeholder="请输入产权比例"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="房产税计税原值" name="houseTaxOrgValue">
              <a-input-number
                v-model:value="ruleForm.houseTaxOrgValue"
                :min="0"
                :precision="2"
                class="!w-[100%]"
                placeholder="请输入房产税计税原值"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="增值税率(%)" name="addTaxRate">
              <a-input-number
                v-model:value="ruleForm.addTaxRate"
                addon-after="%"
                :min="0"
                :precision="2"
                class="w-[100%]"
                placeholder="请输入增值税率"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="从价月税率(%)" name="leaseMonthRate">
              <a-input-number
                v-model:value="ruleForm.leaseMonthRate"
                addon-after="%"
                :min="0"
                :precision="2"
                class="w-[100%]"
                placeholder="请输入从价月税率"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="已租赁面积(㎡)" name="haveLeaseArea">
              <a-input-number
                v-model:value="ruleForm.haveLeaseArea"
                addon-after="㎡"
                :min="0"
                :precision="2"
                class="w-[100%]"
                placeholder="请输入已租赁面积"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="土地面积(㎡)" name="landArea">
              <a-input-number
                v-model:value="ruleForm.landArea"
                addon-after="㎡"
                :min="0"
                :precision="2"
                class="w-[100%]"
                placeholder="请输入土地面积"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="土地使用税年费标准" name="landUseMonthlyRate">
              <a-input-number
                v-model:value="ruleForm.landUseMonthlyRate"
                addon-after="元"
                :min="0"
                :precision="2"
                class="w-[100%]"
                placeholder="请输入土地使用税年费标准"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="取得日期" name="getDate">
              <a-date-picker
                picker="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                v-model:value="ruleForm.getDate"
                placeholder="请选择取得日期"
                class="w-[100%]"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="处置日期" name="dealDate">
              <a-date-picker
                picker="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                v-model:value="ruleForm.dealDate"
                placeholder="请选择处置日期"
                class="w-[100%]"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="接收日期" name="receiveDate">
              <a-date-picker
                picker="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                v-model:value="ruleForm.receiveDate"
                placeholder="请选择接收日期"
                class="w-[100%]"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="视同销售计提税费" name="stSaleJTRate">
              <a-select v-model:value="ruleForm.stSaleJTRate" placeholder="请选择视同销售计提税费" allow-clear>
                <a-select-option v-for="item in isOrNotDic" :key="item.dictKey" :value="item.dictKey">
                  {{ item.dictValue }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </template>
      <template v-if="processList[4].status == 1">
        <div class="text-[16px] font-bold mb-[12px] mt-[12px]">附件</div>
        <a-form-item label="附件" name="attachmentIds">
          <files-upload v-model="ruleForm.attachmentIds" :biz-id="ruleForm.id"></files-upload>
        </a-form-item>
      </template>
    </a-form>

    <template #footer>
      <a-button type="primary" @click="handleConfirm" :loading="submitLoading">提交</a-button>
      <a-button v-if="curIndex" type="primary" ghost @click="handlePrev">上一步</a-button>
      <a-button v-if="curIndex !== 4" type="primary" ghost @click="handleNext">下一步</a-button>
      <a-button type="primary" ghost @click="handleStash" :loading="stashLoading">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>
<script setup>
import { projectPage } from '@/views/projects/apis.js'
import { message } from 'ant-design-vue'
import { isOrNotDic } from '@/store/modules/dict.js'
import { getQueryTreeList, submit, stash, edit } from '../apis'
const emits = defineEmits(['loadData'])
const curIndex = ref(0)
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data) => {
  if (data) {
    Object.assign(ruleForm, data, {
      pcaCode: data.pcaCode.split(','),
      buildYear: String(data.buildYear || '')
    })
  }
  visible.value = true
  // loadMenuList()
}
defineExpose({ open })

const processList = reactive([
  { name: '快速开始', status: 1 },
  { name: '基础信息', status: 0 },
  { name: '土地及建筑物信息', status: 0 },
  { name: '税务信息', status: 0 },
  { name: '附件信息', status: 0 }
])

const ruleForm = reactive({
  id: '',
  ownerCover: '',
  ownerInside: '',
  ownerRealImage: '',
  name: '',
  number: '',
  status: '',
  assetsType: '',
  wyProject: '',
  wyBuilding: '',
  wyFloor: '',
  province: '',
  city: '',
  area: '',
  pcaCode: [],
  detailAddress: '',
  collectionCompany: '',
  ownerCompany: '',
  manageCompany: '',
  treeId: '',
  ownerNumber: '',
  warrantsDate: '',
  acquisitionMethod: '',
  propertyRightStatus: '',
  proxies: '',
  isUnionCertificate: undefined,
  propertyUse: '',
  landNature: '',
  landPrice: '',
  landConstructionSituation: '',
  landRent: '',
  arrearsAmount: '',
  houseType: '',
  structureArea: '',
  floorArea: '',
  buildStructrue: '',
  buildYear: '',
  layerNum: '',
  totalLayerNum: '',
  layerHight: '',
  houseModel: '',
  firefightingRate: '',
  houseSafeRate: '',
  propertyRate: '',
  houseTaxOrgValue: '',
  addTaxRate: '',
  leaseMonthRate: '',
  priceDiscRate: '',
  haveLeaseArea: '',
  landArea: '',
  landUseMonthlyRate: '',
  getDate: '',
  dealDate: '',
  receiveDate: '',
  stSaleJTRate: '',
  haveCreateLeaseUnit: '',
  remark: '',
  createBy: '',
  createTime: '',
  updateBy: '',
  updateTime: '',
  auditBy: '',
  auditTime: '',
  attachmentIds: '',
  sourceBillId: '',
  sourceBillEntryId: ''
})
const checkNum = (_rule, value) => {
  if (!value) {
    return Promise.resolve()
  }
  if (ruleForm.layerNum && ruleForm.totalLayerNum && ruleForm.layerNum > ruleForm.totalLayerNum) {
    return Promise.reject('层数不能大于总层数')
  }
  return Promise.resolve()
}
const rules = computed(() => ({
  name: [{ required: [1, 4].includes(curIndex.value), message: '请输入资产名称', trigger: ['blur', 'change'] }],
  assetsType: [{ required: [1, 4].includes(curIndex.value), message: '请选择资产类型', trigger: ['change'] }],
  detailAddress: [{ required: [1, 4].includes(curIndex.value), message: '请输入地址', trigger: ['blur', 'change'] }],
  collectionCompany: [
    { required: [1, 4].includes(curIndex.value), message: '请选择租金归集公司', trigger: ['change'] }
  ],
  ownerCompany: [{ required: [1, 4].includes(curIndex.value), message: '请选择资产权属公司', trigger: ['change'] }],
  manageCompany: [{ required: [1, 4].includes(curIndex.value), message: '请选择物业管理公司', trigger: ['change'] }],
  treeId: [{ required: [1, 4].includes(curIndex.value), message: '请选择产权分类', trigger: ['change'] }],
  ownerNumber: [{ required: [1, 4].includes(curIndex.value), message: '请输入产权号', trigger: ['blur'] }],
  proxies: [{ required: [1, 4].includes(curIndex.value), message: '请输入代管委托方', trigger: ['blur'] }],
  isUnionCertificate: [
    { required: [1, 4].includes(curIndex.value), message: '请选择房地权证合一', trigger: ['change'] }
  ],
  landNature: [{ required: [1, 4].includes(curIndex.value), message: '请选择使用权类型', trigger: ['change'] }],
  structureArea: [{ required: [2, 4].includes(curIndex.value), message: '请输入建筑面积', trigger: ['blur'] }],
  buildStructrue: [{ required: [2, 4].includes(curIndex.value), message: '请选择建筑结构', trigger: ['change'] }],
  layerNum: [{ required: false, validator: checkNum, trigger: ['blur'] }],
  totalLayerNum: [{ required: false, validator: checkNum, trigger: ['blur'] }],
  houseSafeRate: [{ required: [2, 4].includes(curIndex.value), message: '请选择房屋安全等级', trigger: ['change'] }],
  landUseMonthlyRate: [
    { required: [3, 4].includes(curIndex.value), message: '请输入土地使用税年费标准', trigger: ['blur'] }
  ]
}))

const formRef = ref()
const submitLoading = ref(false)
// 提交
const handleConfirm = async () => {
  await formRef.value.validate()
  submitLoading.value = true
  try {
    const par = Object.assign({}, ruleForm, {
      pcaCode: ruleForm.pcaCode.join(',')
    })
    await submit(par)
    message.success('提交成功')
    submitLoading.value = false
    emits('loadData')
  } finally {
    submitLoading.value = false
  }
}
// 上一步
const handlePrev = () => {
  processList[curIndex.value].status = 0
  processList[curIndex.value - 1].status = 1
  curIndex.value--
}
// 下一步
const handleNext = async () => {
  await formRef.value.validate()
  processList[curIndex.value].status = 2
  processList[curIndex.value + 1].status = 1
  curIndex.value++
}
// 暂存
const stashLoading = ref(false)
const handleStash = async () => {
  stashLoading.value = true
  try {
    const par = Object.assign({}, ruleForm, {
      pcaCode: ruleForm.pcaCode.join(',')
    })
    await (ruleForm.id ? edit(par) : stash(par))
    message.success('暂存成功')
    stashLoading.value = false
    emits('loadData')
  } finally {
    stashLoading.value = false
  }
}

// 取消
const handleCancel = () => {
  ruleForm.id = ''
  ruleForm.ownerCover = ''
  ruleForm.ownerInside = ''
  ruleForm.ownerRealImage = ''
  ruleForm.name = ''
  ruleForm.number = ''
  ruleForm.status = ''
  ruleForm.assetsType = ''
  ruleForm.wyProject = ''
  ruleForm.wyBuilding = ''
  ruleForm.wyFloor = ''
  ruleForm.province = ''
  ruleForm.city = ''
  ruleForm.area = ''
  ruleForm.pcaCode = []
  ruleForm.detailAddress = ''
  ruleForm.collectionCompany = ''
  ruleForm.ownerCompany = ''
  ruleForm.manageCompany = ''
  ruleForm.treeId = ''
  ruleForm.ownerNumber = ''
  ruleForm.warrantsDate = ''
  ruleForm.acquisitionMethod = ''
  ruleForm.propertyRightStatus = ''
  ruleForm.proxies = ''
  ruleForm.isUnionCertificate = undefined
  ruleForm.propertyUse = ''
  ruleForm.landNature = ''
  ruleForm.landPrice = ''
  ruleForm.landConstructionSituation = ''
  ruleForm.landRent = ''
  ruleForm.arrearsAmount = ''
  ruleForm.houseType = ''
  ruleForm.structureArea = ''
  ruleForm.floorArea = ''
  ruleForm.buildStructrue = ''
  ruleForm.buildYear = ''
  ruleForm.layerNum = ''
  ruleForm.totalLayerNum = ''
  ruleForm.layerHight = ''
  ruleForm.houseModel = ''
  ruleForm.firefightingRate = ''
  ruleForm.houseSafeRate = ''
  ruleForm.propertyRate = ''
  ruleForm.houseTaxOrgValue = ''
  ruleForm.addTaxRate = ''
  ruleForm.leaseMonthRate = ''
  ruleForm.priceDiscRate = ''
  ruleForm.haveLeaseArea = ''
  ruleForm.landArea = ''
  ruleForm.landUseMonthlyRate = ''
  ruleForm.getDate = ''
  ruleForm.dealDate = ''
  ruleForm.receiveDate = ''
  ruleForm.stSaleJTRate = ''
  ruleForm.haveCreateLeaseUnit = ''
  ruleForm.remark = ''
  ruleForm.createBy = ''
  ruleForm.createTime = ''
  ruleForm.updateBy = ''
  ruleForm.updateTime = ''
  ruleForm.auditBy = ''
  ruleForm.auditTime = ''
  ruleForm.attachmentIds = ''
  ruleForm.sourceBillId = ''
  ruleForm.sourceBillEntryId = ''
  curIndex.value = 0

  Object.assign(processList, [
    { name: '快速开始', status: 1 },
    { name: '基础信息', status: 0 },
    { name: '土地及建筑物信息', status: 0 },
    { name: '税务信息', status: 0 },
    { name: '附件信息', status: 0 }
  ])
  formRef.value.clearValidate()
  visible.value = false
}

// 设置地址数据
const setAddress = () => {
  if (ruleForm.pcaCode && ruleForm.pcaCode.length) {
    ruleForm.province = ruleForm.pcaCode[0]
    ruleForm.city = ruleForm.pcaCode[1]
    ruleForm.area = ruleForm.pcaCode[2]
    return
  }
  ruleForm.province = ''
  ruleForm.city = ''
  ruleForm.area = ''
}
</script>

<style scoped lang="less">
.progress-wrapper {
  width: 100%;
  display: flex;
  align-items: center;

  .progress-item {
    flex-grow: 1;
    display: flex;
    align-items: center;
    white-space: nowrap;
    gap: 12px;
    .item-step {
      flex-shrink: 0;
      width: 24px;
      height: 24px;
      display: inline-block;
      border: 1px solid #8992a3;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      &.active {
        background: #165dff;
        .step-num {
          color: #fff;
        }
      }
      .a-icon-dagou {
        color: #fff;
      }
      .step-num {
        color: #8992a3;
      }
    }
    .item-name {
      flex-shrink: 0;
      color: #8992a3;
      &.active {
        color: #1d335c;
      }
    }
    .item-line {
      flex-grow: 1;
      padding: 1px;
      display: inline-block;
      // min-width: 200px;
      background: #d7dae0;
      &.active {
        background: #165dff;
      }
    }
  }
  .progress-item:last-child {
    .item-line {
      display: none;
    }
  }
}
</style>
