<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex justify-between !my-[24px]">
      <div class="flex">
        <a-radio-group class="!m-0" v-model:value="activeTab" style="margin: 8px" @change="activeTabChange">
          <a-radio-button v-for="item in tabs" :key="item.value" :value="item.value">
            {{ item.name }}
          </a-radio-button>
        </a-radio-group>

        <a-button class="ml-[16px]" type="primary" @click="handleAdd">
          <span class="a-icon-plus mr-[8px]"></span>
          新建
        </a-button>
        <a-button @click="handleImport">
          <span class="a-icon-import-right mr-[8px]"></span>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <span class="a-icon-export-right mr-[8px]"></span>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button class="ml-[16px]">
            <span>批量操作</span>
            <i class="a-icon-arrow-down text-[12px] ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <span class="primary-btn" @click="handleRemove(false)">删除</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>

        <a-button class="ml-[16px]" @click="onTableChange({ pageNo: 1, pageSize: pageTableData.pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <search-more v-model="search" :search-list="searchList" @searchChange="onTableChange"></search-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="pageTableData.list"
      :columns="columns"
      :loading="pageTableData.tableLoading"
      :scroll="{ y: pageTableData.tableHeight, x: 2000 }"
      :pagination="pageTableData.pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="rowView(record)">查看</span>
          <!--  暂存 已撤回 审核不通过 才有 -->
          <!-- v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)"  -->
          <span class="primary-btn" @click="rowEdit(record)">编辑</span>
          <!-- 待审批 -->
          <span class="primary-btn" v-if="record.status === 'AUDITING'" @click="rowBack(record)">撤回</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <span class="primary-btn" @click="rowViewApprove(record)">查看审批</span>
                </a-menu-item>
                <a-menu-item v-if="['BACK'].includes(record.status)">
                  <span class="primary-btn" @click="rowResubmit(record, 1)">重新提交</span>
                </a-menu-item>
                <a-menu-item v-if="['TEMP'].includes(record.status)">
                  <span class="primary-btn" @click="rowResubmit(record)">提交</span>
                </a-menu-item>
                <!-- 暂存 已撤回 才有删除 -->
                <a-menu-item v-if="['TEMP', 'BACK'].includes(record.status)">
                  <span class="primary-btn" @click="handleRemove(record)">删除</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>

    <add-edit ref="addEditRef" @load-data="onTableChange"></add-edit>
    <!-- 查看 -->
    <detail ref="detailRef" @load-data="onTableChange" :ids="tableIds || []"></detail>
    <!-- 导入 -->
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入资产跟踪"
      :download-fn="() => requestFuncObj.exportExcel('资产跟踪数据导入模板.xls', { id: 0 })"
      :upload-fn="requestFuncObj.importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pageTableData.pagination.pageSize })"
    ></common-import>
  </div>
</template>
<script setup>
// renderDict,
import Detail from './components/TrackDetail.vue'
import { renderDictTag } from '@/utils/render'
import usePageTable from '@/hooks/usePageTable'
import AddEdit from './components/AddEdit.vue'
import useTableSelection from '@/hooks/useTableSelection'
import {
  getAllList,
  getIdleAssetTrackingList,
  getOccupyAssetTrackingList,
  getBorrowAssetTrackingList,
  getSelfAssetTrackingList,
  idleExportExcel,
  occupyExportExcel,
  borrowExportExcel,
  selfExportExcel,
  idleImportExcel,
  occupyImportExcel,
  borrowImportExcel,
  selfImportExcel,
  idleBatchDel,
  occupyBatchDel,
  borrowBatchDel,
  selfBatchDel,
  getIdleQueryById,
  getOccupyQueryById,
  getBorrowQueryById,
  getSelfQueryById,
  queryIdleAssetActivateByMainId,
  queryOccupyAssetActivateByMainId,
  queryBorrowAssetTrackingInfoByMainId,
  querySelfAssetTrackingInfoByMainId,
  idleBack,
  occupyBack,
  borrowBack,
  selfBack,
  idleSubmit,
  occupySubmit,
  borrowSubmit,
  selfSubmit
} from './apis'
import { Modal, message } from 'ant-design-vue'
onMounted(() => {
  onTableChange()
  // 资产详情跳转过来新增
  if (route.query.adding) {
    addEditRef?.value.open()
  }
})

const route = useRoute()
const pageTitle = computed(() => route.meta.title)
const tabs = ref([
  { value: 0, name: '全部跟踪' },
  { value: 1, name: '闲置' },
  { value: 2, name: '占用' },
  { value: 3, name: '借用' },
  { value: 4, name: '自用' }
])
const activeTab = ref(0)
const columnSetRef = ref()
const search = ref({
  number: '',
  trackingType: '',
  manageCompany: '',
  bizDate: '',
  status: ''
})
const searchList = reactive([
  {
    label: '单据编号',
    name: 'number',
    type: 'input',
    placeholder: '请输入单据编号'
  },
  {
    label: '跟踪类型',
    name: 'trackingType',
    type: 'dic',
    placeholder: '请选择跟踪类型',
    code: 'CT_BASE_ENUM_TrackingType'
  },
  // {
  //   label: '跟踪资产',
  //   name: 'name',
  //   type: 'input',
  //   placeholder: '请输入跟踪资产'
  // },
  {
    label: '物业管理公司',
    name: 'manageCompany',
    type: 'deptTree',
    placeholder: '请选择物业管理公司'
  },
  {
    label: '业务日期',
    name: 'bizDate',
    type: 'date',
    placeholder: '请选择业务日期'
  },
  {
    label: '业务状态',
    name: 'status',
    type: 'dic',
    placeholder: '请选择业务状态',
    code: 'CT_BASE_ENUM_AuditStatus'
  }
  // {
  //   label: '数据状态',
  //   name: 'name',
  //   type: 'input',
  //   placeholder: '请输入单据编号'
  // },
  // {
  //   label: '房产名称',
  //   name: 'name',
  //   type: 'input',
  //   placeholder: '请输入单据编号'
  // },
  // {
  //   label: '产权',
  //   name: 'name',
  //   type: 'input',
  //   placeholder: '请输入单据编号'
  // },
  // {
  //   label: '归集公司',
  //   name: 'name',
  //   type: 'input',
  //   placeholder: '请输入单据编号'
  // },
  // {
  //   label: '权属公司',
  //   name: 'name',
  //   type: 'input',
  //   placeholder: '请输入单据编号'
  // },
  // {
  //   label: '资产类型',
  //   name: 'name',
  //   type: 'input',
  //   placeholder: '请输入单据编号'
  // }
])
const getTypeRequestObj = (type) => {
  const funcObj = {
    back: null,
    submit: null
  }
  switch (type) {
    case 'Idle':
      funcObj.back = idleBack
      funcObj.submit = idleSubmit
      break
    case 'Occupy':
      funcObj.back = occupyBack
      funcObj.submit = occupySubmit
      break
    case 'Borrow':
      funcObj.back = borrowBack
      funcObj.submit = borrowSubmit
      break
    case 'Self':
      funcObj.back = selfBack
      funcObj.submit = selfSubmit
      break
  }
  return funcObj
}
const requestFuncObj = computed(() => {
  const funcObj = {
    exportExcel: null,
    importExcel: null,
    batchDel: null
  }
  switch (activeTab.value) {
    case 1:
      funcObj.exportExcel = idleExportExcel
      funcObj.importExcel = idleImportExcel
      funcObj.batchDel = idleBatchDel
      funcObj.back = idleBack
      break
    case 2:
      funcObj.exportExcel = occupyExportExcel
      funcObj.importExcel = occupyImportExcel
      funcObj.batchDel = occupyBatchDel
      funcObj.back = occupyBack
      break
    case 3:
      funcObj.exportExcel = borrowExportExcel
      funcObj.importExcel = borrowImportExcel
      funcObj.batchDel = borrowBatchDel
      funcObj.back = borrowBack
      break
    case 4:
      funcObj.exportExcel = selfExportExcel
      funcObj.importExcel = selfImportExcel
      funcObj.batchDel = selfBatchDel
      funcObj.back = selfBack
      break
  }
  return funcObj
})
const searchJson = computed(() => {
  let params = {}
  switch (activeTab.value) {
    case 0:
      params = {}
      break
    case 1:
      params = {
        number: '',
        manageCompany: '',
        bizDate: '',
        trackingType: '',
        status: '',
        houseOwner: '',
        unUsedBeginDate: '',
        unUsedEndDate: '',
        unUsedArea: '',
        unUsedTime: '',
        unUsedReason: '',
        remark: '',
        createBy: '',
        createTime: '',
        updateBy: '',
        updateTime: '',
        auditBy: '',
        auditTime: '',
        sourceBillId: '',
        sourceBillEntryId: '',
        delFlag: ''
      }
      break
    case 2:
      params = {
        number: '',
        manageCompany: '',
        bizDate: '',
        trackingType: '',
        status: '',
        houseOwner: '',
        occupyArea: '',
        occupyPerson: '',
        occupyReason: '',
        occupyBeginDate: '',
        occupyEndDate: '',
        changeUse: '',
        illegal: '',
        remark: '',
        createBy: '',
        createTime: '',
        updateBy: '',
        updateTime: '',
        auditBy: '',
        auditTime: '',
        sourceBillId: '',
        sourceBillEntryId: '',
        delFlag: ''
      }
      break
    case 3:
      params = {
        number: '',
        manageCompany: '',
        bizDate: '',
        trackingType: '',
        status: '',
        remark: '',
        createBy: '',
        createTime: '',
        updateBy: '',
        updateTime: '',
        auditBy: '',
        auditTime: '',
        sourceBillId: '',
        sourceBillEntryId: '',
        delFlag: ''
      }
      break
    case 4:
      params = {
        number: '',
        manageCompany: '',
        bizDate: '',
        trackingType: '',
        status: '',
        remark: '',
        createBy: '',
        createTime: '',
        updateBy: '',
        updateTime: '',
        auditBy: '',
        auditTime: '',
        sourceBillId: '',
        sourceBillEntryId: '',
        delFlag: ''
      }
      break
  }
  return JSON.stringify(params)
})
const pageObj = computed(() => {
  let func = ''
  let params = {}
  switch (activeTab.value) {
    case 0:
      params = { assetTrackingVO: searchJson.value }
      func = getAllList
      break
    case 1:
      params = { idleAssetTracking: searchJson.value }
      func = getIdleAssetTrackingList
      break
    case 2:
      params = { occupyAssetTracking: searchJson.value }
      func = getOccupyAssetTrackingList
      break
    case 3:
      params = { borrowAssetTracking: searchJson.value }
      func = getBorrowAssetTrackingList
      break
    case 4:
      params = { selfAssetTracking: searchJson.value }
      func = getSelfAssetTrackingList
      break
  }
  return { func, params }
})
const pageTableData = ref({
  tableLoading: false,
  pagination: {},
  list: [],
  onTableFetch() {},
  tableHeight: ''
})
pageTableData.value = usePageTable(pageObj.value.func)
const tableIds = computed(() => {
  return pageTableData.value.list.map((item) => item.id)
})
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  pageTableData.value.onTableFetch({ pageNo: current ?? pageNo, pageSize, ...pageObj.value.params })
}
const activeTabChange = () => {
  pageTableData.value = usePageTable(pageObj.value.func)
  onTableChange()
}

const defaultColumns = [
  { title: '单据编号', dataIndex: 'number', width: 180, fixed: 'left' },
  { title: '管理公司', dataIndex: 'manageCompany_dictText', width: 150 },
  {
    title: '状态',
    dataIndex: 'status',
    width: 200,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '业务日期', dataIndex: 'bizDate' },
  { title: '资产', dataIndex: 'houseOwner_dictText' },
  { title: '跟踪类型', dataIndex: 'trackingType_dictText' },
  { title: '备注', dataIndex: 'remark' },
  { title: '创建时间', dataIndex: 'createTime' },
  { title: '提交人', dataIndex: 'createBy_dictText' },
  { title: '审核时间', dataIndex: 'auditTime' },
  { title: '操作', dataIndex: 'action', width: 200, fixed: 'right' }
]

const columns = computed(() => columnSetRef.value?.columns)
const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(pageTableData.value.list, 'id', true)
// 新增
const addEditRef = ref()
const handleAdd = () => {
  addEditRef.value.open()
}
const detailRef = ref()
// 查看
const rowView = (row) => {
  detailRef?.value.open(row)
}
// 编辑
const rowEdit = async (row) => {
  let requestFunc = null
  let recordsByMainIdFunc = null
  const dataForm = {}

  if (row.trackingType === 'Idle') {
    requestFunc = getIdleQueryById
    recordsByMainIdFunc = queryIdleAssetActivateByMainId
  }
  if (row.trackingType === 'Occupy') {
    requestFunc = getOccupyQueryById
    recordsByMainIdFunc = queryOccupyAssetActivateByMainId
  }
  if (row.trackingType === 'Borrow') {
    requestFunc = getBorrowQueryById
    recordsByMainIdFunc = queryBorrowAssetTrackingInfoByMainId
  }
  if (row.trackingType === 'Self') {
    requestFunc = getSelfQueryById
    recordsByMainIdFunc = querySelfAssetTrackingInfoByMainId
  }
  if (requestFunc) {
    const { result } = await requestFunc(row.id)
    const { result: records } = await recordsByMainIdFunc(row.id)
    if (row.trackingType === 'Idle') {
      dataForm.idleAssetActivateList = records
    }
    if (row.trackingType === 'Occupy') {
      dataForm.occupyAssetActivateList = records
    }
    if (row.trackingType === 'Borrow') {
      dataForm.borrowAssetTrackingInfoList = records
    }
    if (row.trackingType === 'Self') {
      dataForm.selfAssetTrackingInfoList = records
    }
    addEditRef?.value.open({ ...result, ...dataForm })
  }
  // addEditRef?.value.open(row)
}
// 撤回操作
const rowBack = (row) => {
  Modal.confirm({
    title: '提示',
    content: '确定撤回？',
    centered: true,
    onOk: async () => {
      const { back } = getTypeRequestObj(row.trackingType)
      const data = await back({ id: row.id })
      message.success(data.message)
      const pageNo = pageTableData.value.current
      onTableChange({ pageNo })
    }
  })
}
// 查看审批
const rowViewApprove = () => {}
// 提交（重新提交）
const rowResubmit = (row, type = 0) => {
  Modal.confirm({
    title: '提示',
    content: type ? '确认重新提交？' : '确认提交？',
    centered: true,
    onOk: async () => {
      const { submit } = getTypeRequestObj(row.trackingType)
      const data = await submit({ id: row.id })
      message.success(data.message)
      const pageNo = pageTableData.value.current
      onTableChange({ pageNo })
    }
  })
}
const handleRemove = (data) => {
  Modal.confirm({
    title: '提示',
    content: '确认删除当前资产跟踪？',
    centered: true,
    onOk: async () => {
      await requestFuncObj.value.batchDel(data ? data.id : selectedRowKeys.value.join(','))
      message.success('删除成功')
      let pageNo = pageTableData.value.pagination.current
      if (
        pageNo > 1 &&
        ((data && pageTableData.value.list.length === 1) ||
          (!data && selectedRowKeys.value.length === pageTableData.value.list.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pageTableData.value.pagination.pageSize })
    }
  })
}

// 导入
const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}
// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await requestFuncObj.value.exportExcel('资产跟踪数据导出.xls', search)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}
</script>
