<template>
  <a-modal
    v-model:open="visible"
    :title="title"
    width="600px"
    class="common-modal"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form
      :model="ruleForm"
      ref="formRef"
      :rules="rules"
      label-align="left"
      :label-col="{ style: { width: '100px' } }"
      autocomplete="off"
    >
      <a-form-item label="计费项名称" name="name">
        <a-input v-model:value="ruleForm.name" placeholder="请输入方案名称" :maxlength="50" show-count />
      </a-form-item>
      <a-form-item label="计费单位" name="unit">
        <a-select :options="[]" v-model:value="ruleForm.unit" placeholder="请选择计费单位" allow-clear />
      </a-form-item>
      <a-form-item label="计费单价" name="unitPrice">
        <div>
          <div class="flex justify-between mb-[12px]">
            <div>
              <span class="mr-[5px]">阶梯定价</span>
              <a-switch v-model:checked="ruleForm.checked" />
            </div>
            <div class="cursor-pointer" @click="addUnitPrice">
              <span class="a-icon-plus text-[14px] font-bold text-primary mr-[5px]"></span>
              <span>添加阶梯</span>
            </div>
          </div>
          <div
            class="flex items-center gap-x-[12px] mb-[12px]"
            v-for="(item, index) in ruleForm.unitPrice"
            :key="index"
          >
            <span
              class="a-icon-remove cursor-pointer text-[16px] font-bold text-error"
              @click="delUnitPrice(item.index)"
            ></span>
            <a-input v-model:value="item.a" prefix="第" suffix="件起"></a-input>
            <a-input v-model:value="item.b" suffix="元"></a-input>
          </div>
        </div>
      </a-form-item>
      <a-form-item label="封顶规则" name="isCap">
        <div class="mb-[12px]">
          <span class="mr-[5px]">开启封顶</span>
          <a-switch v-model:checked="ruleForm.isCap" />
        </div>
        <div class="flex items-center gap-x-[12px]">
          <a-input v-model:value="ruleForm.c" suffix="件内封顶"></a-input>
          <a-input v-model:value="ruleForm.d" suffix="元"></a-input>
        </div>
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="ruleForm.remark" placeholder="请输入备注" :maxlength="200" show-count />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup>
// 弹窗可见性
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}) => {
  visible.value = true
  if (data.id) {
    Object.assign(ruleForm, data)
  }
}
defineExpose({ open })
const title = computed(() => {
  return ruleForm.id ? '编辑订单计费项' : '新增订单计费项'
})
const ruleForm = reactive({
  id: '',
  name: '',
  unit: undefined,
  unitPrice: [{}, {}],
  remark: ''
})
const rules = {
  unit: [{ required: true, message: '请输入方案编码', trigger: ['blur'] }],
  name: [{ required: true, message: '请方案名称', trigger: ['blur'] }],
  unitPrice: [{ type: 'array', required: true, message: '请输入计费单价', trigger: ['change'] }]
}

const addUnitPrice = () => {
  ruleForm.unitPrice.push({ a: '', b: '' })
}
const delUnitPrice = (item, index) => {
  ruleForm.unitPrice.splice(index, 1)
}
// 提交
const handleConfirm = () => {}
// 取消
const handleCancel = () => {
  Object.assign(ruleForm, {
    id: '',
    unit: '',
    name: '',
    unitPrice: [],
    condition: []
  })
  visible.value = false
}
</script>
