<template>
  <a-radio-group v-model:value="paymentType" button-style="solid">
    <a-radio-button v-for="item in detail.billList" :key="item.paymentType" :value="item.paymentType">
      {{ item.paymentType_dictText }}
    </a-radio-button>
  </a-radio-group>
  <div class="flex items-center justify-between my-[16px]">
    <div class="flex items-center">
      <a-button>
        <i class="a-icon-export-right"></i>
        导出
      </a-button>
      <s-input v-model="keyword" class="ml-[16px]"></s-input>
    </div>
    <columns-set ref="columnsRef" :default-columns="defaultBillColumns"></columns-set>
  </div>
  <a-table
    :data-source="tableData"
    :columns="billColumns"
    :pagination="false"
    :scroll="{ x: 1200, y: '50vh' }"
  ></a-table>
</template>

<script setup>
const { detail } = defineProps({
  detail: { required: true, type: Object }
})

const defaultBillColumns = [
  { title: '款项', dataIndex: 'paymentType_dictText', fixed: 'left' },
  { title: '缴交周期', dataIndex: 'originalRent' },
  { title: '金额', dataIndex: 'paymentAmount', customRender: ({ text }) => `${text}元/月` },
  { title: '每期金额', dataIndex: 'collectionCompany_dictText' },
  {
    title: '起止日期',
    dataIndex: 'receiveBeginDate',
    customRender: ({ record }) => `${record.receiveBeginDate} ~ ${record.receiveEndDate}`
  },
  { title: '账单生成规则', dataIndex: 'areaManager_dictText' },
  { title: '应收日', dataIndex: 'receiveDate' },
  { title: '备注', dataIndex: 'remark' },
  { title: '递增规则', dataIndex: 'propertyUse_dictText' }
]
const columnsRef = ref()
const billColumns = computed(() => columnsRef.value?.columns)

const paymentType = ref('全部')
const keyword = ref('')

const tableData = computed(() => {
  if (!(detail.billList && detail.billList.length)) return []
  const data = detail.billList.find((i) => i.paymentType === paymentType.value)
  return data ? data.contractDetailBillsList : []
})
</script>
