<template>
  <a-drawer
    v-model:open="visible"
    class="edit-contract-type-drawer common-drawer"
    title="合同退租清算申请"
    placement="right"
    width="1072px"
    @close="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <a-form
        :model="form"
        :rules="rules"
        ref="formRef"
        :label-col="{ style: { width: '110px' } }"
        label-align="left"
        autocomplete="off"
      >
        <a-form-item label="名称" name="name">
          <a-input v-model:value="form.name" :maxlength="50" show-count></a-input>
        </a-form-item>
        <a-form-item label="管理公司" name="manageCompany">
          <dept-tree-select v-model="form.manageCompany" type="company"></dept-tree-select>
        </a-form-item>
        <a-form-item label="合同类别" name="contractCategory">
          <dict-select v-model="form.contractCategory" code="CT_BASE_ENUM_ContractType_ContractCategory"></dict-select>
        </a-form-item>
        <a-form-item label="款项性质" name="paymentNature">
          <dict-select v-model="form.paymentNature" code="CT_BASE_ENUM_ContractType_PaymentNature"></dict-select>
        </a-form-item>
        <a-form-item label="状态" name="status">
          <dict-select v-model="form.status" code="CT_BASE_ENUM_BaseStatus"></dict-select>
        </a-form-item>
        <a-form-item label="纳入计提印花税" name="isIncludeJtStampTax">
          <a-select v-model:value="form.isIncludeJtStampTax" allow-clear>
            <a-select-option :value="true">是</a-select-option>
            <a-select-option :value="false">否</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="保留小数" name="isKeepDecimals">
          <a-select v-model:value="form.isKeepDecimals" allow-clear>
            <a-select-option :value="true">是</a-select-option>
            <a-select-option :value="false">否</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="自动清算" name="isAutoClear">
          <a-select v-model:value="form.isAutoClear" allow-clear>
            <a-select-option :value="true">是</a-select-option>
            <a-select-option :value="false">否</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea
            v-model:value="form.remark"
            show-count
            :maxlength="500"
            :auto-size="{ minRows: 5, maxRows: 5 }"
          ></a-textarea>
        </a-form-item>
      </a-form>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { edit, add, detail } from '../apis.js'
import { message } from 'ant-design-vue'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  if (id) {
    loadDetail(id)
  }
}

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await detail({ id })
  for (const key in form) {
    form[key] = result[key]
  }
  loading.value = false
}

const form = reactive({
  id: '',
  name: '',
  number: '',
  manageCompany: '',
  contractCategory: '',
  paymentNature: '',
  isKeepDecimals: undefined,
  isIncludeJtStampTax: undefined,
  isAutoClear: undefined,
  status: 'ENABLE',
  remark: ''
})

const rules = {
  manageCompany: [{ required: true, message: '请选择管理公司', trigger: 'change' }],
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  contractCategory: [{ required: true, message: '请选择合同类别', trigger: 'change' }],
  paymentNature: [{ required: true, message: '请选择款项性质', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    form.id ? await edit(form) : await add(form)
    confirmLoading.value = false
    handleCancel()
    message.success('提交成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  form.id = ''
  form.name = ''
  form.number = ''
  form.manageCompany = ''
  form.contractCategory = ''
  form.paymentNature = ''
  form.isKeepDecimals = undefined
  form.isIncludeJtStampTax = undefined
  form.isAutoClear = undefined
  form.status = 'ENABLE'
  form.remark = ''
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.edit-contract-type-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 10px;
  }
  .ant-form-item {
    width: calc(50% - 5px);
    &:last-child {
      width: 100%;
    }
  }
}
</style>
