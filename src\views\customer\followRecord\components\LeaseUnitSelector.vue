<template>
  <div class="lease-unit-select-input" @click="openModal" @mouseenter="onmouseenter" @mouseleave="onmouseleave">
    <span :placeholder="placeholder">{{ displayValue }}</span>
    <i class="a-icon-close-solid text-[rgba(23,43,82,0.25)]" @click.stop="handleClear" v-if="showClearBtn"></i>
    <i class="a-icon-arrow-down text-[rgba(23,43,82,0.25)]" v-else></i>
  </div>

  <a-modal
    v-model:open="visible"
    :title="title"
    width="1072px"
    class="lease-unit-selector-modal common-modal"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="mb-[12px]">
      <s-input v-model="searchParams.name" placeholder="搜索名称" class="!w-[280px]" @input="handleSearch"></s-input>
      <filter-more
        :params="searchParams"
        :search-list="searchList"
        width="320px"
        label-width="100px"
        @query="handleFilterSearch"
      ></filter-more>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{
        selectedRowKeys,
        onChange: onSelectChange,
        type: multiple ? 'checkbox' : 'radio'
      }"
      :scroll="{ y: '50vh' }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <status-tag :dict-value="record.status" dict-code="CT_BASE_ENUM_BaseStatus" type="dot"></status-tag>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { getUserList } from '@/views/system/user/apis'
import { projectPage } from '@/views/projects/apis.js'
import { getPage } from '@/views/assets/manage/apis'
import region from '@/json/region.json'
import { getLeaseUnitList } from '@/views/leaseUnit/manage/apis/leaseUnit'

const { modelValue, multiple } = defineProps({
  modelValue: { required: true, type: [String, Array] },
  placeholder: { type: String, default: '' },
  title: { type: String, default: '选择租赁单元' },
  multiple: { type: Boolean, default: false },
  maxCount: { type: Number, default: undefined }
})

const emit = defineEmits(['update:modelValue', 'change'])

const { list, pagination, tableLoading, onTableFetch } = usePageTable(getLeaseUnitList)
const { selectedRows, selectedRowKeys, onSelectChange } = useTableSelection(list, 'id', false, !multiple)

const visible = ref(false)
const showClear = ref(false)

const displayValue = ref('')
const searchParams = reactive({
  name: undefined,
  pcaCode: [],
  useType: undefined,
  leaseUse: undefined,
  areaManager: undefined,
  status: undefined,
  bizStatus: undefined,
  supportFacility: undefined,
  effectDate: undefined,
  expireDate: undefined,
  layerNum: undefined,
  propertyUse: undefined,
  wyProject: undefined,
  number: undefined
})

const searchList = reactive([
  {
    label: '区域',
    name: 'pcaCode',
    type: 'cascader',
    placeholder: '请选择区域',
    options: region,
    fieldNames: { label: 'label', value: 'value', children: 'children' }
  },
  { label: '使用类型', name: 'useType', type: 'dict-select', placeholder: '请选择使用类型', code: 'CT_BAS_UseType' },
  { label: '租赁用途', name: 'leaseUse', type: 'dict-select', code: 'CT_BAS_LeaseUse', placeholder: '请选择租赁用途' },
  {
    label: '片区管理员',
    name: 'areaManager',
    type: 'api-select',
    placeholder: '请选择片区管理员',
    asyncFn: getUserList
  },
  {
    label: '单据状态',
    name: 'status',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_AuditStatus',
    placeholder: '请选择单据状态'
  },
  {
    label: '业务状态',
    name: 'bizStatus',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_LeaseUnit_BizStatus',
    placeholder: '请选择业务状态'
  },
  { label: '配套设施', name: 'supportFacility', type: 'input', placeholder: '请输入配套设施' },
  { label: '生效日期', name: 'effectDate', type: 'date', placeholder: '请选择生效日期' },
  { label: '到期日期', name: 'expireDate', type: 'date', placeholder: '请选择到期日期' },
  { label: '层数/总层数', name: 'layerNum', type: 'input', placeholder: '请输入层数' },
  {
    label: '产权用途',
    name: 'propertyUse',
    type: 'dict-select',
    code: 'CT_BAS_PropertyUse',
    placeholder: '请选择产权用途'
  },
  { label: '所属项目', name: 'wyProject', type: 'api-select', placeholder: '请选择所属项目', asyncFn: projectPage },
  { label: '关联资产', name: 'houseOwner', type: 'api-select', placeholder: '请选择关联资产', asyncFn: getPage },
  { label: '单元编码', name: 'number', type: 'input', placeholder: '请输入单元编码' }
])

const showClearBtn = computed(() => modelValue && modelValue.length > 0 && showClear.value)

const columns = [
  { title: '租赁单元名称', dataIndex: 'name', width: 200, fixed: 'left' },
  { title: '地址', dataIndex: 'detailAddress', width: 160, ellipsis: true },
  { title: '使用类型', dataIndex: 'useType_dictText', width: 120 },
  { title: '租赁面积(m²)', dataIndex: 'leaseArea', width: 120 },
  { title: '租赁用途', dataIndex: 'leaseUse_dictText', width: 120 },
  { title: '片区管理员', dataIndex: 'areaManager_dictText', width: 120, ellipsis: true },
  { title: '单据状态', dataIndex: 'status', width: 120 },
  { title: '业务状态', dataIndex: 'bizStatus_dictText', width: 120 },
  { title: '配套设施', dataIndex: 'supportFacility', width: 160, ellipsis: true },
  { title: '生效日期', dataIndex: 'effectDate', width: 120 },
  { title: '到期日期', dataIndex: 'expireDate', width: 120 },
  { title: '层数/总层数', dataIndex: 'layerNum', width: 120 },
  { title: '产权用途', dataIndex: 'propertyUse_dictText', width: 120, ellipsis: true },
  { title: '所属项目', dataIndex: 'wyProject_dictText', width: 120, ellipsis: true },
  { title: '单元编码', dataIndex: 'number', width: 200 }
]

/**
 * 打开选择模态框
 */
const openModal = () => {
  visible.value = true
  onTableChange({ pageNo: 1, pageSize: 10 })
}

/**
 * 取消选择模态框
 */
const handleCancel = () => {
  visible.value = false
}

/**
 * 清空选择
 */
const handleClear = () => {
  emit('update:modelValue', multiple ? [] : '')
  emit('change', multiple ? [] : '')
  displayValue.value = ''
}

/**
 * 确认选择
 */
const handleConfirm = () => {
  const value = multiple ? selectedRowKeys.value : selectedRowKeys.value[0]
  displayValue.value = selectedRows.value.map((item) => item.name).join(', ')
  emit('update:modelValue', value)
  emit('change', value)
  handleCancel()
}

/**
 * 搜索输入处理
 */
let timer
const handleSearch = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

/**
 * 筛选搜索处理
 */
const handleFilterSearch = () => {
  pagination.value.current = 1
  onTableChange()
}

/**
 * 表格分页变化处理
 */
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams })
}

/**
 * 鼠标进入事件
 */
const onmouseenter = () => {
  if (modelValue) {
    showClear.value = true
  }
}

/**
 * 鼠标离开事件
 */
const onmouseleave = () => {
  showClear.value = false
}

/**
 * 监听 modelValue 变化，根据 multiple 状态处理选中数据
 */
watch(
  () => modelValue,
  async (val) => {
    // 重置状态
    selectedRowKeys.value = []
    selectedRows.value = []
    displayValue.value = ''

    if (multiple) {
      if (!Array.isArray(val) || val.length === 0) {
        return
      }

      // 获取选中项的详细信息
      const id = val.join(',')
      const { result } = await getLeaseUnitList({ id })

      // 更新选中状态
      selectedRowKeys.value = [...val]
      selectedRows.value = result.records || []
      displayValue.value = selectedRows.value.map((item) => item.name).join(', ')
    } else {
      if (!val || typeof val !== 'string') {
        return
      }

      // 获取选中项的详细信息
      const { result } = await getLeaseUnitList({ id: val })

      // 更新选中状态
      selectedRowKeys.value = [val]
      selectedRows.value = result.records || []
      displayValue.value = selectedRows.value.length > 0 ? selectedRows.value[0].name : ''
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped>
.lease-unit-select-input {
  width: 100%;
  cursor: pointer;
  height: 40px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  font-size: 14px;
  background-color: #fff;
  transition: border-color 0.2s;
  &:hover {
    border-color: var(--color-primary);
  }
  & > span:empty {
    &::after {
      content: attr(placeholder);
      color: rgba(0, 0, 0, 0.25);
    }
  }
}
</style>

<style lang="less">
.lease-unit-selector-modal {
  .ant-select.ant-select-in-form-item {
    width: 100px !important;
  }
}
</style>
