<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex items-center justify-between mt-[24px] mb-[16px]">
      <div>
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus mr-1"></i>
          新建
        </a-button>
        <a-button @click="handleImport">
          <i class="a-icon-import-right mr-1"></i>
          导入
        </a-button>
        <a-button @click="handleExport">
          <i class="a-icon-download mr-1"></i>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="handleBatchDelete">
                <i class="a-icon-delete mr-1"></i>
                批量删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>
            <i class="a-icon-more mr-1"></i>
            更多操作
            <i class="a-icon-down"></i>
          </a-button>
        </a-dropdown>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="searchParams.number"
          placeholder="搜索编号"
          class="ml-[40px] !w-[280px]"
          @input="handleSearch"
        ></s-input>
        <filter-more
          :params="searchParams"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
    </div>

    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      row-key="id"
      :scroll="{ x: 1200, y: tableHeight }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <span class="primary-btn" @click="handleDetail(record)">查看</span>
            <template v-if="record.status === 'DRAFT'">
              <span class="primary-btn" @click="handleEdit(record)">编辑</span>
              <span class="primary-btn" @click="handleSubmit(record)">提交</span>
              <span class="danger-btn" @click="handleDelete(record)">删除</span>
            </template>
            <template v-else-if="record.status === 'PENDING_AUDIT'">
              <span class="primary-btn" @click="handleAudit(record)">审核</span>
            </template>
            <template v-else-if="record.status === 'AUDITED'">
              <span class="primary-btn" @click="handleUnAudit(record)">反审核</span>
            </template>
          </a-space>
        </template>
      </template>
    </a-table>

    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('退款申请导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
    />
    <edit-refund ref="editDrawerRef" @refresh="onTableChange" />
    <refund-detail ref="detailDrawerRef" :data-list="list" @refresh="onTableChange" @edit="handleEdit" />
  </div>
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import {
  getRefundReqBillList,
  submitRefundReqBill,
  auditRefundReqBill,
  unAuditRefundReqBill,
  deleteRefundReqBill,
  batchDeleteRefundReqBill,
  importExcel,
  exportExcel
} from './apis'
import EditRefund from './components/EditRefund.vue'
import RefundDetail from './components/RefundDetail.vue'

const route = useRoute()

const commonImportRef = ref()
const editDrawerRef = ref()
const detailDrawerRef = ref()

const exportLoading = ref(false)

const searchParams = reactive({
  number: undefined,
  customer: undefined,
  contract: undefined,
  refundReqAmount: undefined,
  bizDate: undefined,
  billSource: undefined,
  status: undefined,
  operator: undefined,
  remark: undefined,
  createTime: undefined
})

const searchList = [
  { label: '退款客户', name: 'customer', type: 'user-select' },
  { label: '退款合同', name: 'contract', type: 's-input' },
  { label: '申请退款金额', name: 'refundReqAmount', type: 's-input' },
  { label: '业务日期', name: 'bizDate', type: 'date' },
  { label: '来源', name: 'billSource', type: 's-input' },
  { label: '状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_AuditStatus' },
  { label: '提交人', name: 'operator', type: 'user-select' },
  { label: '备注', name: 'remark', type: 's-input' },
  { label: '创建时间', name: 'createTime', type: 'date' }
]

const columns = [
  { title: '单据编号', dataIndex: 'number', fixed: 'left', width: 200 },
  { title: '退款客户', dataIndex: 'customer_dictText', width: 160, ellipsis: true },
  { title: '退款合同', dataIndex: 'contract', width: 160, ellipsis: true },
  { title: '申请退款金额', dataIndex: 'refundReqAmount', width: 120 },
  { title: '业务日期', dataIndex: 'bizDate', width: 120 },
  { title: '来源', dataIndex: 'billSource_dictText', width: 120 },
  { title: '状态', dataIndex: 'status_dictText', width: 100 },
  { title: '提交人', dataIndex: 'operator_dictText', width: 120 },
  { title: '备注', dataIndex: 'remark', width: 200, ellipsis: true },
  { title: '创建时间', dataIndex: 'createTime', width: 160, ellipsis: true },
  { title: '操作', dataIndex: 'action', fixed: 'right', width: 140 }
]

const pageTitle = computed(() => route.meta.title)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getRefundReqBillList)
const { selectedRowKeys, onSelectChange } = useTableSelection(list, 'id')

/**
 * 打开新建退款申请记录弹窗
 */
const handleAdd = () => {
  editDrawerRef.value.open()
}

/**
 * 编辑退款申请记录
 * @param {Object} record - 要编辑的记录对象
 */
const handleEdit = (record) => {
  editDrawerRef.value.open(record)
}

/**
 * 查看退款申请记录详情
 * @param {Object} record - 要查看的记录对象
 */
const handleDetail = (record) => {
  detailDrawerRef.value.open(record)
}

/**
 * 提交退款申请记录
 * @param {Object} record - 要提交的记录对象
 */
const handleSubmit = (record) => {
  Modal.confirm({
    title: '确认提交',
    content: '确认提交该退款申请记录？',
    async onOk() {
      await submitRefundReqBill({ id: record.id })
      message.success('提交成功')
      onTableChange()
    }
  })
}

/**
 * 审核退款申请记录
 * @param {Object} record - 要审核的记录对象
 */
const handleAudit = (record) => {
  Modal.confirm({
    title: '确认审核',
    content: '确认审核该退款申请记录？',
    async onOk() {
      await auditRefundReqBill({ id: record.id })
      message.success('审核成功')
      onTableChange()
    }
  })
}

/**
 * 反审核退款申请记录
 * @param {Object} record - 要反审核的记录对象
 */
const handleUnAudit = (record) => {
  Modal.confirm({
    title: '确认反审核',
    content: '确认反审核该退款申请记录？',
    async onOk() {
      await unAuditRefundReqBill({ id: record.id })
      message.success('反审核成功')
      onTableChange()
    }
  })
}

/**
 * 删除单条退款申请记录
 * @param {Object} record - 要删除的记录对象
 */
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: '确认删除该退款申请记录？',
    async onOk() {
      await deleteRefundReqBill({ id: record.id })
      message.success('删除成功')
      onTableChange()
    }
  })
}

/**
 * 批量删除选中的退款申请记录
 */
const handleBatchDelete = () => {
  if (!selectedRowKeys.value.length) {
    message.warning('请选择要删除的记录')
    return
  }

  Modal.confirm({
    title: '确认删除',
    content: `确认删除选中的 ${selectedRowKeys.value.length} 条记录？`,
    async onOk() {
      await batchDeleteRefundReqBill({ ids: selectedRowKeys.value.join(',') })
      message.success('删除成功')
      onTableChange()
    }
  })
}

/**
 * 打开导入弹窗
 */
const handleImport = () => {
  commonImportRef.value.open()
}

/**
 * 导出退款申请记录清单
 */
const handleExport = async () => {
  if (exportLoading.value) return
  exportLoading.value = true
  try {
    await exportExcel('退款申请记录清单.xls', searchParams)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}

let timer
const handleSearch = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams })
}

onMounted(() => {
  onTableChange()
})
</script>
