<template>
  <a-modal
    v-model:open="visible"
    title="选择抵扣欠款明细"
    width="1200px"
    class="common-modal"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form autocomplete="off" layout="inline" class="!mb-[16px]">
      <a-form-item label="客户名称">
        <a-input
          v-model:value="searchParams.customer"
          placeholder="搜索客户名称"
          @input="handleInput"
          style="width: 200px"
          allow-clear
        >
          <template #prefix>
            <i class="a-icon-search text-primary"></i>
          </template>
        </a-input>
      </a-form-item>
      <a-form-item label="合同">
        <a-input
          v-model:value="searchParams.contract"
          placeholder="搜索合同"
          @input="handleInput"
          style="width: 200px"
          allow-clear
        >
          <template #prefix>
            <i class="a-icon-search text-primary"></i>
          </template>
        </a-input>
      </a-form-item>
      <a-form-item label="租赁单元">
        <a-input
          v-model:value="searchParams.leaseUnit"
          placeholder="搜索租赁单元"
          @input="handleInput"
          style="width: 200px"
          allow-clear
        >
          <template #prefix>
            <i class="a-icon-search text-primary"></i>
          </template>
        </a-input>
      </a-form-item>
    </a-form>

    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{
        selectedRowKeys,
        onChange: onSelectChange,
        type: 'checkbox'
      }"
      :scroll="{ y: '50vh', x: 1800 }"
      @change="onTableChange"
    ></a-table>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { f7DeductionDetailList } from '../apis'

const emit = defineEmits(['confirm'])

const visible = ref(false)
let timer

const searchParams = reactive({
  customer: undefined,
  contract: undefined,
  leaseUnit: undefined
})

const { list, pagination, tableLoading, onTableFetch } = usePageTable(f7DeductionDetailList)
const { selectedRows, selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id', false)

/**
 * 弹窗表格列配置 - 严格按需求顺序
 */
const columns = [
  { title: '单据ID', dataIndex: 'parent', width: 120, fixed: 'left' },
  { title: '客户名称', dataIndex: 'customer', width: 150 },
  { title: '合同', dataIndex: 'contract', width: 150 },
  { title: '租赁单元', dataIndex: 'leaseUnit', width: 150 },
  { title: '款项类型', dataIndex: 'paymentType', width: 120 },
  { title: '状态', dataIndex: 'status', width: 100 },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 120 },
  { title: '款项金额', dataIndex: 'paymentAmount', width: 120 },
  { title: '调整金额', dataIndex: 'receiveAmountAdjust', width: 120 },
  { title: '应收金额', dataIndex: 'actualReceiveAmount', width: 120 },
  { title: '已收金额', dataIndex: 'paid', width: 120 },
  { title: '剩余金额', dataIndex: 'residual', width: 120 },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '结清日期', dataIndex: 'receiveEndDate', width: 120 }
]

/**
 * 打开选择器
 */
const open = () => {
  visible.value = true
  onTableChange({ pageNo: 1, pageSize: 10 })
}

/**
 * 取消选择
 */
const handleCancel = () => {
  clearSelection()
  visible.value = false
}

/**
 * 确认选择
 */
const handleConfirm = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请至少选择一条明细记录')
    return
  }

  emit('confirm', selectedRows.value)
  handleCancel()
}

/**
 * 搜索输入处理
 */
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

/**
 * 表格分页变化处理
 */
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams })
}

defineExpose({
  open
})
</script>
