<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex items-center justify-between mt-[24px] mb-[16px]">
      <div>
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus mr-1"></i>
          新建
        </a-button>
        <a-button @click="handleImport">
          <i class="a-icon-import-right mr-1"></i>
          导入
        </a-button>
        <a-button @click="handleExport">
          <i class="a-icon-download mr-1"></i>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <template #overlay>
            <a-menu>
              <a-menu-item key="batchDelete" @click="handleBatchDelete">批量删除</a-menu-item>
            </a-menu>
          </template>
          <a-button>
            操作
            <i class="a-icon-arrow-down ml-1"></i>
          </a-button>
        </a-dropdown>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="searchParams.number"
          placeholder="搜索编号"
          class="ml-[40px] !w-[280px]"
          @input="handleSearch"
        ></s-input>
        <filter-more
          :params="searchParams"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <!-- 表格区域 -->
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      :scroll="{ x: 1200, y: tableHeight }"
      row-key="id"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <status-tag :dict-value="record.status" dict-code="CT_BASE_ENUM_BaseStatus" type="dot"></status-tag>
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleDetail(record)">查看</span>
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="handleEdit(record)">编辑</a-menu-item>
                <a-menu-item @click="handleSubmit(record)" v-if="record.status === '0'">提交</a-menu-item>
                <a-menu-item @click="handleDelete(record)">删除</a-menu-item>
              </a-menu>
            </template>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
          </a-dropdown>
        </template>
      </template>
    </a-table>

    <edit-transfer-deduction ref="editDrawerRef" @refresh="onTableChange" />
    <transfer-deduction-detail ref="detailDrawerRef" :data-list="list" @refresh="onTableChange" @edit="handleEdit" />
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('转款抵扣导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
    ></common-import>
  </div>
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import {
  getTransferDeductionList,
  submitTransferDeduction,
  deleteTransferDeduction,
  batchDeleteTransferDeduction,
  importExcel,
  exportExcel
} from './apis'
import EditTransferDeduction from './components/EditTransferDeduction.vue'
import TransferDeductionDetail from './components/TransferDeductionDetail.vue'

const route = useRoute()

const exportLoading = ref(false)
const commonImportRef = ref()
const editDrawerRef = ref()
const detailDrawerRef = ref()
const columnSetRef = ref()

const searchParams = reactive({
  number: undefined,
  sourceContract: undefined,
  transferDeductionContract: undefined,
  transferDeductionAmount: undefined,
  status: undefined,
  createTime: undefined,
  bizDate: undefined,
  operator: undefined,
  remark: undefined
})

const searchList = [
  { label: '款项来源合同', name: 'sourceContract', type: 's-input', placeholder: '请输入款项来源合同' },
  {
    label: '款项转款抵扣合同',
    name: 'transferDeductionContract',
    type: 's-input',
    placeholder: '请输入款项转款抵扣合同'
  },
  { label: '转款抵扣金额', name: 'transferDeductionAmount', type: 's-input', placeholder: '请输入转款抵扣金额' },
  {
    label: '审核状态',
    name: 'status',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_AuditStatus',
    placeholder: '请选择审核状态'
  },
  { label: '创建时间', name: 'createTime', type: 'date', placeholder: '请选择创建时间' },
  { label: '业务时间', name: 'bizDate', type: 'date', placeholder: '请选择业务时间' },
  { label: '提交人', name: 'operator', type: 'user-select', placeholder: '请选择提交人' },
  { label: '备注', name: 'remark', type: 's-input', placeholder: '请输入备注' }
]

const defaultColumns = [
  { title: '单据编号', dataIndex: 'number', width: 200, fixed: 'left' },
  { title: '款项来源合同', dataIndex: 'sourceContract', width: 160, ellipsis: true },
  { title: '款项转款抵扣合同', dataIndex: 'transferDeductionContract', width: 160, ellipsis: true },
  { title: '转款抵扣金额', dataIndex: 'transferDeductionAmount', width: 120 },
  { title: '审核状态', dataIndex: 'status', width: 120 },
  { title: '创建时间', dataIndex: 'createTime', width: 120 },
  { title: '业务时间', dataIndex: 'bizDate', width: 120 },
  { title: '提交人', dataIndex: 'operator_dictText', width: 120 },
  { title: '备注', dataIndex: 'remark', width: 160, ellipsis: true },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

const columns = computed(() => columnSetRef.value?.columns || defaultColumns)
const pageTitle = computed(() => route.meta.title)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getTransferDeductionList)
const { selectedRowKeys, onSelectChange } = useTableSelection(list, 'id')

/**
 * 打开新建转款抵扣记录的抽屉组件
 */
const handleAdd = () => {
  editDrawerRef.value.open()
}

/**
 * 打开编辑转款抵扣记录的抽屉组件
 * @param {Object} record - 转款抵扣记录数据
 */
const handleEdit = (record) => {
  editDrawerRef.value.open(record)
}

/**
 * 打开查看转款抵扣记录详情的抽屉组件
 * @param {Object} record - 转款抵扣记录数据
 */
const handleDetail = (record) => {
  detailDrawerRef.value.open(record)
}

/**
 * 打开导入数据的模态框
 */
const handleImport = () => {
  commonImportRef.value.open()
}

/**
 * 导出转款抵扣记录数据到Excel文件
 */
const handleExport = async () => {
  if (exportLoading.value) return
  exportLoading.value = true
  try {
    await exportExcel('转款抵扣记录.xls', searchParams)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}

/**
 * 提交转款抵扣记录进行审核
 * @param {Object} record - 转款抵扣记录数据
 */
const handleSubmit = (record) => {
  Modal.confirm({
    title: '确认提交',
    content: '确认提交该转款抵扣记录？',
    onOk: async () => {
      await submitTransferDeduction({ id: record.id })
      message.success('提交成功')
      onTableChange()
    }
  })
}

/**
 * 删除单条转款抵扣记录
 * @param {Object} record - 转款抵扣记录数据
 */
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: '确认删除该转款抵扣记录？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      await deleteTransferDeduction({ id: record.id })
      message.success('删除成功')
      onTableChange()
    }
  })
}

/**
 * 批量删除选中的转款抵扣记录
 */
const handleBatchDelete = () => {
  if (!selectedRowKeys.value.length) {
    message.warning('请选择要删除的记录')
    return
  }

  Modal.confirm({
    title: '确认删除',
    content: `确认删除选中的 ${selectedRowKeys.value.length} 条记录？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      await batchDeleteTransferDeduction({ ids: selectedRowKeys.value.join(',') })
      message.success('删除成功')
      selectedRowKeys.value = []
      onTableChange()
    }
  })
}

let timer
/**
 * 搜索输入防抖处理，延迟600ms执行搜索
 */
const handleSearch = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams })
}

onMounted(() => {
  onTableChange()
})
</script>
