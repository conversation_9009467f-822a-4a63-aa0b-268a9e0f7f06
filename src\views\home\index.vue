<template>
  <div class="!p-[0px] home">
    <img src="@/assets/imgs/home/<USER>" class="w-full" />
    <div class="content">
      <!-- 我的待办 -->
      <div class="flex items-center mb-[10px]">
        <img src="@/assets/svgs/todo.svg" class="w-[24px] h-[24px]" />
        <span class="text-[18px] font-bold text-[#1D335C] mx-[8px]">我的待办</span>
        <span class="ml-[4px] text-[#F03A1D] cursor-pointer">
          <i class="a-icon-warning-solid"></i>
          <span class="mx-[4px]">您有 2 条预警消息，请及时了解并关注</span>
          <i class="a-icon-right-arrow"></i>
        </span>
      </div>
      <a-row :gutter="[16, 16]">
        <a-col v-for="(t, index) in todoList" :key="t.id" :xs="2" :sm="4" :md="6">
          <div
            class="todo cursor-pointer"
            :style="{
              background: `url(${bg('todo', index)}) no-repeat`,
              backgroundSize: '100% 100%'
            }"
          >
            <div class="text-[#495A7A]">{{ t.name }}</div>
            <div class="text-[#1D335C] text-[40px] font-bold">{{ t.num }}</div>
            <div class="primary-btn mt-[10px]">
              <span>前去处理</span>
              <i class="a-icon-right-arrow"></i>
            </div>
          </div>
        </a-col>
      </a-row>

      <!-- 我常用的 -->
      <div class="flex justify-between mt-[40px] mb-[10px]">
        <div class="flex items-center">
          <img src="@/assets/svgs/app.svg" class="w-[24px] h-[24px]" />
          <span class="text-[18px] font-bold text-[#1D335C] mx-[8px]">我常用的</span>
        </div>

        <div class="flex primary-btn custom">
          <i class="a-icon-setting"></i>
          <span>自定义</span>
        </div>
      </div>

      <a-row :gutter="[16, 16]">
        <a-col v-for="(u, index) in useList" :key="u.id" :xs="6" :sm="6" :md="4">
          <div class="use flex items-center cursor-pointer" :style="{ background: bgColor(index) }">
            <div class="use-icon text-[20px] flex items-center justify-center" :style="{ background: iconBg(index) }">
              <i :class="u.icon"></i>
            </div>
            <span class="ml-[10px]">{{ u.name }}</span>
          </div>
        </a-col>
      </a-row>

      <!-- 数据看板 -->
      <div class="flex justify-between mt-[40px] mb-[10px]">
        <div class="flex items-center">
          <img src="@/assets/svgs/chart.svg" class="w-[24px] h-[24px]" />
          <span class="text-[18px] font-bold text-[#1D335C] mx-[8px]">数据看板</span>
        </div>
        <dept-tree-select
          v-model="jtCompany"
          type="company"
          placeholder="全部公司"
          class="w-[240px]"
          clearable
        ></dept-tree-select>
      </div>

      <a-row :gutter="[16, 16]">
        <a-col v-for="(h, index) in houseOwnerList" :key="h.id" :xs="2" :sm="4" :md="6">
          <div
            class="house cursor-pointer"
            :style="{
              backgroundImage: `url(${bg('house', index)})`,
              backgroundRepeat: 'no-repeat',
              backgroundSize: 'inherit 100%',
              backgroundPosition: 'right center'
            }"
          >
            <div class="text-[#495A7A] flex items-center mb-[30px]">
              <i :class="h.icon"></i>
              <span class="ml-[4px]">{{ h.name }}</span>
            </div>
            <a-statistic :value="h.num" class="text-[#1D335C] text-[40px] font-bold" />
          </div>
        </a-col>
      </a-row>

      <a-row :gutter="[16, 16]" class="mt-[16px]">
        <a-col :md="12">
          <a-card class="h-[316px]">
            <div class="flex justify-between">
              <span class="text-[#1D335C] text-[16px]">资产面积状态统计（m³）</span>
            </div>
            <div class="h-[240px]">
              <Chart id="houseOwner-stat" :option="houseOwnerStat" />
            </div>
          </a-card>
        </a-col>
        <a-col :md="12">
          <a-card class="h-[316px]">
            <div class="flex justify-between">
              <span class="text-[#1D335C] text-[16px]">资产面积类型统计（m³）</span>
            </div>
            <div class="h-[240px]">
              <Chart id="houseOwner-type-stat" :option="houseOwnerStat" />
            </div>
          </a-card>
        </a-col>
        <a-col :md="12">
          <a-card class="h-[316px]">
            <div class="flex justify-between">
              <span class="text-[#1D335C] text-[16px]">房产分布情况（m³）</span>
            </div>
            <div class="h-[240px]">
              <Chart id="houseOwner-distribute" :option="distributeStat" />
            </div>
          </a-card>
        </a-col>
        <a-col :md="12">
          <a-card class="h-[316px]">
            <div class="flex justify-between">
              <span class="text-[#1D335C] text-[16px]">合同到期情况统计</span>
            </div>
            <div class="h-[240px]">
              <Chart id="contract-stat" :option="distributeStat" />
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
import * as echarts from 'echarts'

const jtCompany = ref('')
const todoList = reactive([
  { id: 1, name: '待审批', num: 1, url: '' },
  { id: 2, name: '我发起未完结', num: 1, url: '' },
  { id: 3, name: '已办未完结', num: 1, url: '' },
  { id: 4, name: '未读通知', num: 1, url: '' }
])

const useList = reactive([
  { id: 1, name: '新增客户', icon: 'a-icon-new-customer', url: '' },
  { id: 2, name: '添加客户跟进', icon: 'a-icon-new-customer', url: '' },
  { id: 3, name: '新建项目', icon: 'a-icon-new-file', url: '' },
  { id: 4, name: '导入账单明细', icon: 'a-icon-new-file', url: '' },
  { id: 3, name: '新增合同', icon: 'a-icon-new-file', url: '' },
  { id: 4, name: '核销营收/缴费', icon: 'a-icon-new-file', url: '' },
  { id: 3, name: '合同管理', icon: 'a-icon-new-file', url: '' },
  { id: 4, name: '资料归档', icon: 'a-icon-new-file', url: '' }
])

const houseOwnerList = reactive([
  { id: 1, name: '资产总面积（m³）', num: 100000, icon: 'a-icon-house', url: '' },
  { id: 2, name: '资产个数（个）', num: 8888, icon: 'a-icon-coins', url: '' },
  { id: 3, name: '本年资产应收（万元）', num: 666888, icon: 'a-icon-money2', url: '' },
  { id: 4, name: '本年资产已收（万元）', num: 666666, icon: 'a-icon-wallet', url: '' }
])

const bg = (type, index) => {
  return new URL(`/src/assets/imgs/home/<USER>
}

const iconBg = (index) => {
  const colorList = ['#165DFF', '#05DFEB', '#FAB700', '#F03A1D']
  return colorList[index % 4]
}

const bgColor = (index) => {
  const colorList = ['#EAF0FE', '#E5FCFE', '#FFF8E5', '#FEEBEA']
  return colorList[index % 4]
}

const echartData = [
  { name: 'A类', value: '3720' },
  { name: 'B类', value: '2920' },
  { name: 'C类', value: '2200' },
  { name: 'D类', value: '1420' }
]

const formatNumber = (num) => {
  const reg = /(?=(\B)(\d{3})+$)/g
  return num.toString().replace(reg, ',')
}
const total = echartData.reduce((a, b) => {
  return a + Number(b.value)
}, 0)
const title = '资产面积'
const houseOwnerStat = reactive({
  title: [
    {
      text: `{name|${title}}\n{val|${formatNumber(total)}}`,
      top: 'center',
      left: 'center',
      textStyle: {
        rich: {
          name: { fontSize: 14, fontWeight: 'normal', color: '#666666', padding: [10, 0] },
          val: { fontSize: 32, fontWeight: 'bold', color: '#333333' }
        }
      }
    }
  ],
  legend: {
    orient: 'vertical',
    icon: 'rect',
    x: '80%',
    y: 'center',
    itemWidth: 12,
    itemHeight: 12,
    align: 'left',
    textStyle: {
      rich: { name: { fontSize: 12 }, value: { fontSize: 16, padding: [0, 5, 0, 15] }, unit: { fontSize: 12 } }
    },
    formatter(name) {
      let res = echartData.filter((v) => v.name === name)
      res = res[0] || {}
      const unit = res.unit || ''
      return `{name|${name}}  {value|${res.value}}{unit|${unit}}`
    }
  },
  series: [
    {
      type: 'pie',
      radius: ['45%', '60%'],
      center: ['50%', '50%'],
      data: echartData,
      hoverAnimation: false,
      itemStyle: { normal: { borderWidth: 2 } },
      labelLine: { normal: { length: 20, length2: 120, lineStyle: { color: '#e6e6e6' } } },
      label: {
        normal: {
          show: false,
          formatter: (params) => {
            return `{icon|●}{name|${params.name}}{value|${formatNumber(params.value)}}`
          },
          padding: [0, -100, 25, -100],
          rich: {
            icon: { fontSize: 16 },
            name: { fontSize: 14, padding: [0, 10, 0, 4], color: '#666666' },
            value: { fontSize: 18, fontWeight: 'bold', color: '#333333' }
          }
        }
      }
    }
  ]
})

const distributeStat = reactive({
  grid: { top: '10%', left: '12%', right: '5%', bottom: '20%' },
  tooltip: {
    trigger: 'axis',
    backgroundColor: '#3A4667',
    borderColor: '#3A4667',
    textStyle: { color: '#fff' },
    formatter: '{b} : {c}',
    axisPointer: { type: 'cross', crossStyle: { color: '#999' } }
  },
  legend: {
    icon: 'rect',
    top: 10,
    right: 5,
    itemWidth: 10,
    itemHeight: 10,
    textStyle: { fontSize: 12, color: '#FFEB3B' }
  },
  xAxis: {
    type: 'category',
    axisPointer: {
      type: 'shadow'
    },
    data: ['意向', '在租', '空置', '纠纷', '自用', '占用', '出售中']
  },
  yAxis: {
    type: 'value',
    splitLine: { show: true, lineStyle: { type: 'dashed' } },
    axisLabel: { formatter: '{value}', textStyle: { color: '#B4C0CC', fontSize: 12 } },
    name: '',
    nameTextStyle: { color: '#B3CFFF', fontSize: 12 }
  },
  series: [
    {
      type: 'bar',
      name: '',
      barWidth: 27,
      itemStyle: {
        normal: {
          borderRadius: 6,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 1, color: 'rgba(57,126,255,1)', opacity: 1 },
            { offset: 0, color: 'rgba(0,216,254,1)', opacity: 0.6 }
          ])
        }
      },
      data: [3000, 2300, 2399, 3330, 3440, 2220, 2700]
    }
  ]
})
</script>

<style lang="less" scoped>
.home {
  background: linear-gradient(to bottom, #fff 10%, #f7feff 100%);
}

.content {
  max-width: 1680px;
  margin: -100px auto 0;
  padding: 0 20px 20px;
}

.todo {
  height: 154px;
  border-radius: 10px;
  padding: 20px;
}

.use {
  height: 64px;
  padding: 0 20px;
  color: #1d335c;
  border-radius: 8px;
  border: 1px solid #e6e9f0;

  &-icon {
    width: 28px;
    height: 28px;
    color: #fff;
    border-radius: 8px;
  }
}

.house {
  height: 132px;
  padding: 20px;
  padding: 20px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e6e9f0;
}

.custom {
  cursor: pointer;
  height: 32px;
  line-height: 32px;
  padding: 0 10px;
  background: #eaf0fe;
  border-radius: 8px;
}
</style>
