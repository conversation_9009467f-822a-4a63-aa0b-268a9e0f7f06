<template>
  <div>
    <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c]">附件</h4>
    <div class="p-4">
      <files-upload v-model="formData.files" />
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
})

// 监听文件列表变化，更新 attachmentIds
watch(
  () => props.formData.files,
  (newFiles) => {
    if (Array.isArray(newFiles)) {
      props.formData.attachmentIds = newFiles.join(',')
    } else if (typeof newFiles === 'string') {
      props.formData.attachmentIds = newFiles
    } else {
      props.formData.attachmentIds = ''
    }
  },
  { immediate: true, deep: true }
)
</script>

<style scoped lang="less"></style>
