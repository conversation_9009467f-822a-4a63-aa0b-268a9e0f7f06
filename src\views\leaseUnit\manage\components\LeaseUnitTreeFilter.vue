<template>
  <a-select
    v-bind="$attrs"
    ref="selectRef"
    v-model:value="selectedTreeNode"
    placeholder="请选择类别"
    :dropdown-style="{ maxHeight: '400px', overflow: 'auto', padding: '24px' }"
    :dropdown-match-select-width="false"
    allow-clear
    @clear="handleClear"
  >
    <template #dropdownRender>
      <div @mousedown.prevent>
        <a-tree v-model:selected-keys="selectedKeys" :tree-data="treeData" default-expand-all @select="onTreeSelect">
          <template #title="{ name, id }">
            <div class="flex items-center group h-[32px]">
              <span>{{ name }}</span>
              <div class="hidden group-hover:flex">
                <a-tooltip title="添加下级">
                  <span class="mx-1 cursor-pointer text-primary">
                    <i class="a-icon-plus" @click.stop="handleAddTreeNode(id)"></i>
                  </span>
                </a-tooltip>
                <a-tooltip title="编辑">
                  <span class="mx-1 cursor-pointer text-primary">
                    <i class="a-icon-edit" @click.stop="handleEditTreeNode(id)"></i>
                  </span>
                </a-tooltip>
                <a-tooltip title="删除">
                  <span class="mx-1 cursor-pointer text-[#ff4d4f]">
                    <i class="a-icon-remove" @click.stop="handleDeleteTreeNode(id)"></i>
                  </span>
                </a-tooltip>
              </div>
            </div>
          </template>
        </a-tree>
        <div class="flex justify-center border-t border-solid border-[#f0f0f0] pt-[16px]">
          <span class="primary-btn" @click="handleAddRootTreeNode">添加根节点</span>
        </div>
      </div>
    </template>
    <a-select-option v-if="selectedName" :value="selectedTreeNode">
      {{ selectedName }}
    </a-select-option>
  </a-select>

  <!-- 编辑弹窗 -->
  <edit-lease-unit-tree-modal ref="editLeaseUnitTreeModalRef" @success="handleEditSuccess" />
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import { getLeaseUnitTree, deleteLeaseUnitTree } from '../apis/leaseUnitTree'
import EditLeaseUnitTreeModal from './EditLeaseUnitTreeModal.vue'

const emit = defineEmits(['treeNodeChange'])

const selectRef = ref()
const editLeaseUnitTreeModalRef = ref(null)
const treeData = ref([])
const selectedTreeNode = ref()
const selectedName = ref('')
const selectedKeys = ref([])

/**
 * 获取租赁单元树数据
 */
const fetchTreeData = async () => {
  const { result } = await getLeaseUnitTree()
  treeData.value = result || []
}

/**
 * 清空选择状态
 */
const handleClear = () => {
  selectedTreeNode.value = undefined
  selectedName.value = ''
  selectedKeys.value = []
  emit('treeNodeChange', undefined)
}

/**
 * 添加根节点
 */
const handleAddRootTreeNode = () => {
  showEditModal()
}

/**
 * 添加子节点
 * @param {string} parentId 父节点ID
 */
const handleAddTreeNode = (parentId) => {
  showEditModal(null, parentId)
}

/**
 * 编辑树节点
 * @param {string} id 节点ID
 */
const handleEditTreeNode = (id) => {
  const editNode = findNodeById(treeData.value, id)
  if (editNode) {
    showEditModal(editNode)
  }
}

/**
 * 删除树节点
 * @param {string} id 节点ID
 */
const handleDeleteTreeNode = (id) => {
  Modal.confirm({
    title: '提示',
    content: '确定要删除此节点吗？删除后无法恢复！',
    async onOk() {
      await deleteLeaseUnitTree({ id })
      message.success('删除成功')
      fetchTreeData()
    }
  })
}

/**
 * 树节点选择事件处理
 * @param {Array} selectedKeys 选中的节点key数组
 * @param {Object} param1 选择信息对象
 * @param {Object} param1.node 选中的节点对象
 */
const onTreeSelect = (selectedKeys, { node }) => {
  if (selectedKeys.length) {
    selectedTreeNode.value = node.id
    selectedName.value = node.name
    // 选中节点后，让下拉框收起
    if (selectRef.value) {
      selectRef.value.blur()
    }
    emit('treeNodeChange', node.id)
  }
}

/**
 * 编辑成功回调
 */
const handleEditSuccess = () => {
  fetchTreeData()
}

/**
 * 显示编辑弹窗
 * @param {Object|null} editData 编辑数据，为null时表示新增
 * @param {string} parentId 父节点ID
 */
const showEditModal = (editData = null, parentId = '') => {
  editLeaseUnitTreeModalRef.value?.open(editData, parentId)
}

/**
 * 根据ID在树中查找节点
 * @param {Array} nodes 节点数组
 * @param {string} id 节点ID
 * @returns {Object|null} 找到的节点对象或null
 */
const findNodeById = (nodes, id) => {
  for (const node of nodes) {
    if (node.id === id) {
      return node
    }
    if (node.children) {
      const found = findNodeById(node.children, id)
      if (found) return found
    }
  }
  return null
}

watch(selectedTreeNode, (newVal) => {
  if (!newVal) {
    selectedName.value = ''
    selectedKeys.value = []
  }
})

onMounted(() => {
  fetchTreeData()
})
</script>

<style scoped lang="less">
.group:hover {
  .hidden {
    display: flex;
  }
}
</style>
