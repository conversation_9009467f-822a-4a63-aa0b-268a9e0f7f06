import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'

export const getLeaseUnitSplitMergeBillList = (params) => {
  return request({
    method: 'get',
    url: '/biz/basicdatadeal/leaseUnitSplitMergeBill/list',
    params
  })
}

// 通过 id 查询拆合单
export const getLeaseUnitSplitMergeBillById = (params) => {
  return request({
    method: 'get',
    url: '/biz/basicdatadeal/leaseUnitSplitMergeBill/queryById',
    params
  })
}

// 通过 id 查询拆合单源分录
export const queryLeaseUnitSplitMergeBillEntryOriByMainId = (params) => {
  return request({
    method: 'get',
    url: '/biz/basicdatadeal/leaseUnitSplitMergeBill/queryLeaseUnitSplitMergeBillEntryOriByMainId',
    params
  })
}

// 通过 id 查询拆合单目标分录
export const queryLeaseUnitSplitMergeBillEntryDestByMainId = (params) => {
  return request({
    method: 'get',
    url: '/biz/basicdatadeal/leaseUnitSplitMergeBill/queryLeaseUnitSplitMergeBillEntryDestByMainId',
    params
  })
}

// 提交
export const submitLeaseUnitSplitMergeBill = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/leaseUnitSplitMergeBill/submit',
    data
  })
}

// 暂存
export const addLeaseUnitSplitMergeBill = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/leaseUnitSplitMergeBill/add',
    data
  })
}

// 编辑
export const editLeaseUnitSplitMergeBill = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/leaseUnitSplitMergeBill/edit',
    data
  })
}

// 删除
export const deleteLeaseUnitSplitMergeBill = (params) => {
  return request({
    method: 'delete',
    url: '/biz/basicdatadeal/leaseUnitSplitMergeBill/delete',
    params
  })
}

// 批量删除
export const deleteBatchLeaseUnitSplitMergeBill = (data) => {
  return request({
    method: 'delete',
    url: '/biz/basicdatadeal/leaseUnitSplitMergeBill/deleteBatch',
    data
  })
}

// 审核
export const auditLeaseUnitSplitMergeBill = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/leaseUnitSplitMergeBill/audit',
    data
  })
}

// 导出 Excel
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/leaseUnitSplitMergeBill/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}

// 导入 Excel
export const importExcel = (data, controller) => {
  return advanceUpload('/biz/basicdatadeal/leaseUnitSplitMergeBill/importExcel', data, controller)
}
