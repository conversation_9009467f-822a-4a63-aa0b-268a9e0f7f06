<template>
  <div class="w-full">
    <div class="flex items-center overflow-hidden">
      <div class="transfer-panel">
        <div class="panel-header">
          <div class="font-medium text-base">源单元</div>
          <span class="add-btn" @click="handleSelectSourceUnit">
            <i class="a-icon-plus mr-1"></i>
            选择单元
          </span>
        </div>
        <div class="panel-content">
          <div v-if="sourceUnits.length === 0" class="empty-state">
            <a-empty description="暂无数据" />
          </div>
          <div v-else class="p-2">
            <div v-for="item in sourceUnits" :key="item.id" class="unit-item">
              <div class="unit-info">
                <div class="unit-title">{{ item.leaseUnitObject.name }} ({{ item.leaseUnitObject.number }})</div>
                <div class="unit-details">
                  <div class="flex gap-0.5 mt-1">
                    <span class="flex-2/3">
                      地址：{{ item.leaseUnitObject.province }}{{ item.leaseUnitObject.city
                      }}{{ item.leaseUnitObject.district }}{{ item.leaseUnitObject.detailAddress }}
                    </span>
                    <span class="flex-1/3">租赁面积：{{ item.leaseUnitObject.leaseArea }}m²</span>
                  </div>
                  <div class="flex mt-1">
                    <span class="flex-2/3">产权：{{ item.leaseUnitObject.propertyUse_dictText }}</span>
                    <span class="flex-1/3">租赁用途：{{ item.leaseUnitObject.leaseUse_dictText }}</span>
                  </div>
                </div>
              </div>
              <div class="unit-actions">
                <span class="icon-btn" @click="viewSourceUnitDetail(item)">
                  <i class="a-icon-arrow-right"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="transfer-arrow">
        <div class="arrow-icon">
          <i class="a-icon-arrow-right"></i>
        </div>
      </div>

      <div class="transfer-panel">
        <div class="panel-header">
          <div class="font-medium text-base">新单元</div>
          <span class="add-btn" @click="handleAddTargetUnit">
            <i class="a-icon-plus mr-1"></i>
            添加单元
          </span>
        </div>
        <div class="panel-content">
          <div v-if="targetUnits.length === 0" class="empty-state">
            <a-empty description="暂无数据" />
          </div>
          <div v-else class="p-2">
            <div v-for="(item, index) in targetUnits" :key="item.id || index" class="unit-item">
              <div class="unit-info">
                <div class="unit-title">{{ item.leaseUnitObject.name }} ({{ item.leaseUnitObject.number }})</div>
                <div class="unit-details">
                  <div class="flex mt-1">
                    <span class="flex-1">
                      地址：{{ item.leaseUnitObject.province }}{{ item.leaseUnitObject.city
                      }}{{ item.leaseUnitObject.district }}{{ item.leaseUnitObject.detailAddress }}
                    </span>
                    <span class="flex-1">租赁面积：{{ item.leaseUnitObject.leaseArea }}m²</span>
                  </div>
                  <div class="flex mt-1">
                    <span class="flex-1">产权：{{ item.leaseUnitObject.propertyUse_dictText }}</span>
                    <span class="flex-1">租赁用途：{{ item.leaseUnitObject.leaseUse_dictText }}</span>
                  </div>
                </div>
              </div>
              <div class="unit-actions">
                <span class="icon-btn mr-[14px]" @click="removeTargetUnit(index)">
                  <i class="a-icon-remove"></i>
                </span>
                <span class="icon-btn" @click="editTargetUnit(item, index)">
                  <i class="a-icon-edit"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <source-lease-unit-selector ref="sourceLeaseUnitSelectorRef" @change="handleSourceUnitChange" />

    <edit-lease-unit ref="editLeaseUnitRef" @refresh="handleEditLeaseUnitRefresh"></edit-lease-unit>
  </div>
</template>

<script setup>
import { message } from 'ant-design-vue'
import EditLeaseUnit from '@/views/leaseUnit/manage/components/EditLeaseUnit.vue'
import SourceLeaseUnitSelector from './SourceLeaseUnitSelector.vue'

const props = defineProps({
  sourceUnits: {
    type: Array,
    default: () => []
  },
  targetUnits: {
    type: Array,
    default: () => []
  },
  splitMergeType: {
    type: String,
    default: 'split'
  }
})

const emit = defineEmits(['update:sourceUnits', 'update:targetUnits'])

const editingTargetIndex = ref(null)
const editLeaseUnitRef = ref()
const sourceLeaseUnitSelectorRef = ref()

/**
 * 选择源单元
 */
const handleSelectSourceUnit = () => {
  sourceLeaseUnitSelectorRef.value.open(props.sourceUnits)
}

/**
 * 源单元选择变更
 */
const handleSourceUnitChange = (selectedUnits) => {
  if (selectedUnits && selectedUnits.length > 0) {
    emit(
      'update:sourceUnits',
      selectedUnits.map((unit) => ({
        id: unit.id,
        leaseUnitObject: { ...unit }
      }))
    )
    message.success('已选择源租赁单元')
  }
}

/**
 * 查看源单元详情
 */
const viewSourceUnitDetail = (record) => {
  message.info(`查看租赁单元详情：${record.name}`)
}

/**
 * 添加目标单元
 */
const handleAddTargetUnit = () => {
  editingTargetIndex.value = null
  editLeaseUnitRef.value.open()
}

/**
 * 编辑目标单元
 */
const editTargetUnit = (record, index) => {
  editingTargetIndex.value = index
  editLeaseUnitRef.value.open(record.leaseUnitObject)
}

/**
 * 编辑租赁单元刷新
 */
const handleEditLeaseUnitRefresh = (data) => {
  if (!data) return

  const newTargetUnits = [...props.targetUnits]
  const editingIndex = editingTargetIndex.value

  if (editingIndex !== null && editingIndex >= 0 && editingIndex < newTargetUnits.length) {
    newTargetUnits[editingIndex] = {
      id: data.id,
      leaseUnitObject: { ...data }
    }
    message.success('目标租赁单元编辑成功')
  } else {
    newTargetUnits.push({
      id: data.id,
      leaseUnitObject: { ...data }
    })
    message.success('目标租赁单元添加成功')
  }

  emit('update:targetUnits', newTargetUnits)
  editingTargetIndex.value = null
}

/**
 * 移除目标单元
 */
const removeTargetUnit = (index) => {
  const newTargetUnits = [...props.targetUnits]
  newTargetUnits.splice(index, 1)
  emit('update:targetUnits', newTargetUnits)
  message.success('已移除目标租赁单元')
}
</script>

<style scoped lang="less">
.transfer-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-height: 400px;
  background-color: white;
  border: 1px solid #e6e9f0;
  border-radius: 8px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #f7f8fa;
  border-bottom: 1px solid #e6e9f0;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  min-height: 400px;
  padding: 0 16px;
}

.add-btn {
  padding: 5px 12px;
  border: 1px solid #165dff;
  border-radius: 8px;
  color: #165dff;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: #165dff;
    color: white;
  }
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
}

.unit-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  transition: all 0.3s ease;
  padding: 16px 0;
  border-bottom: 1px solid #e6e9f0;
  &:last-child {
    border-bottom: none;
  }
}

.unit-info {
  flex: 1;
  overflow: hidden;
}

.unit-title {
  font-weight: 500;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.unit-details {
  font-size: 12px;
  color: #495a7a;
}

.unit-actions {
  display: flex;
  align-items: center;
  width: 40px;
  flex-direction: row;
  justify-content: flex-end;
}

.transfer-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #eaf0fe;
  width: 32px;
  height: 32px;
  margin: 0 8px;
  border-radius: 4px;
}

.arrow-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
  font-size: 16px;
}

.icon-btn {
  color: #165dff;
  font-size: 20px;
  cursor: pointer;
}
</style>
