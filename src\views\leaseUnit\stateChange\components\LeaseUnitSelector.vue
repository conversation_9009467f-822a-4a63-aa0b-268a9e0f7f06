<template>
  <a-modal
    v-model:open="visible"
    class="common-modal"
    title="选择租赁单元"
    width="1072px"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <!-- 搜索区域 -->
    <div class="mb-[12px]">
      <a-form layout="inline">
        <a-form-item label="租赁单元名称">
          <a-input v-model:value="searchParams.name" placeholder="请输入租赁单元名称" allow-clear />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button class="ml-2" @click="handleResetSearch">重置</a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 已选择提示 -->
    <div v-if="selectedCount > 0" class="mb-[12px] px-[12px] py-[8px] bg-blue-50 rounded border border-blue-200">
      <span class="text-blue-600">已选择 {{ selectedCount }} 个租赁单元</span>
      <a-button type="link" size="small" class="ml-2 p-0" @click="handleClearAll">清空选择</a-button>
    </div>

    <!-- 表格区域 -->
    <a-table
      class="lease-unit-table"
      :columns="columns"
      :data-source="list"
      :loading="tableLoading"
      :pagination="pagination"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
      row-key="id"
      :scroll="{ x: 1200 }"
    ></a-table>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { getLeaseUnitList } from '@/views/leaseUnit/manage/apis/leaseUnit'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'

const emit = defineEmits(['change'])

const visible = ref(false)

const searchParams = reactive({
  name: undefined
})

const { list, pagination, tableLoading, onTableFetch } = usePageTable(getLeaseUnitList)
const { selectedRowKeys, selectedRows, onSelectChange, clearSelection } = useTableSelection(list, 'id', false)

const selectedCount = computed(() => selectedRows.value.length)

const columns = [
  { title: '租赁单元名称', dataIndex: 'name', width: 200, fixed: 'left' },
  { title: '地址', dataIndex: 'detailAddress', width: 160, ellipsis: true },
  { title: '租赁归集公司', dataIndex: 'collectionCompany_dictText', width: 160, ellipsis: true },
  { title: '权属公司', dataIndex: 'ownerCompany_dictText', width: 160, ellipsis: true },
  { title: '片区管理员', dataIndex: 'areaManager_dictText', width: 160, ellipsis: true },
  { title: '业务状态', dataIndex: 'bizStatus_dictText', width: 120 },
  { title: '生效日期', dataIndex: 'effectDate', width: 120 },
  { title: '到期日期', dataIndex: 'expireDate', width: 120 }
]

/**
 * 打开选择器弹窗
 * @param {Array} selectedUnits 已选择的租赁单元列表
 */
const open = (selectedUnits = []) => {
  clearSelection()

  visible.value = true
  onTableChange()

  if (selectedUnits && selectedUnits.length > 0) {
    // 设置初始选中状态
    selectedRows.value = [...selectedUnits]
    selectedRowKeys.value = selectedUnits.map((unit) => unit.id)
  }
}

/**
 * 表格分页变化处理
 */
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams })
}

/**
 * 搜索处理
 */
const handleSearch = () => {
  pagination.value.current = 1
  onTableChange()
}

/**
 * 重置搜索条件
 */
const handleResetSearch = () => {
  searchParams.name = undefined
  pagination.value.current = 1
  onTableChange()
}

/**
 * 确认选择
 */
const handleConfirm = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请至少选择一个租赁单元')
    return
  }

  emit('change', [...selectedRows.value])
  visible.value = false
}

/**
 * 取消选择
 */
const handleCancel = () => {
  clearSelection()
  visible.value = false
}

/**
 * 清空所有选择
 */
const handleClearAll = () => {
  clearSelection()
}

defineExpose({ open })
</script>

<style scoped></style>
