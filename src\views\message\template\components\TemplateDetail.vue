<template>
  <a-drawer v-model:open="visible" class="common-detail-drawer" width="1072px">
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === 0 }"
            @click="handleSwitchDetail(currentIndex - 1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === dataList.length - 1 }"
            @click="handleSwitchDetail(currentIndex + 1)"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <span class="text-primary cursor-pointer mr-[16px]" @click="handleEdit">编辑</span>
        </div>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">{{ detailData.templateTitle }}</h2>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detailData.id || '-' }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detailData.creator || '-' }} 提交于 {{ detailData.createTime || '-' }}</span>
      </div>
      <div id="basic" class="mb-[40px]">
        <h2 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">基础信息</h2>
        <div class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">模板编码：{{ detailData.templateCode || '-' }}</span>
          <span class="w-[50%]">模板类型：{{ detailData.templateType === 'plainText' ? '纯文本' : '富文本' }}</span>
          <span class="w-[50%]">是否启用：{{ detailData.isEnabled ? '已启用' : '未启用' }}</span>
        </div>
      </div>
      <h2 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">模版内容</h2>
      <div class="whitespace-pre-wrap">{{ detailData.templateContent }}</div>
    </a-spin>
  </a-drawer>
</template>

<script setup>
import { getTemplateById } from '../apis'

const { dataList } = defineProps({
  dataList: { type: Array, required: true }
})

const visible = ref(false)
const loading = ref(false)
const editTemplateRef = ref()

const detailData = ref({})

const currentIndex = computed(() => {
  if (!detailData.value.id) return 0
  return dataList.findIndex((i) => i.id === detailData.value.id)
})

/**
 * 打开详情抽屉
 * @param {String} id 模板ID
 */
const open = async (id) => {
  visible.value = true
  await loadDetail(id)
}

/**
 * 加载模板详情
 * @param {String} id 模板ID
 */
const loadDetail = async (id) => {
  loading.value = true
  try {
    const { data } = await getTemplateById({ id })
    detailData.value = data
  } finally {
    loading.value = false
  }
}

/**
 * 编辑当前模板
 */
const handleEdit = () => {
  editTemplateRef.value.open(detailData.value.id)
}

const handleSwitchDetail = (index) => {
  if (index < 0 || index >= dataList.length) return
  const item = dataList[index]
  if (item) {
    loadDetail(item.id)
  }
}

defineExpose({ open })
</script>
