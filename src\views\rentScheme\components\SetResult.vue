设置招标结果
<template>
  <a-modal
    v-model:open="visible"
    title="招标结果"
    width="560px"
    wrap-class-name="common-modal"
    :mask-closable="false"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-radio-group v-model:value="form.result">
      <a-radio value="1">中标</a-radio>
      <a-radio value="2">流标</a-radio>
    </a-radio-group>
    <div class="flex items-center my-[20px]">
      <span class="mr-[8px] shrink-0">中标客户</span>
      <customer-select v-model="form.customer"></customer-select>
    </div>
    <files-upload v-model="form.attachmentIds"></files-upload>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'

const visible = ref(false)
const open = (id) => {
  form.id = id
  visible.value = true
}

const form = reactive({
  id: '',
  result: '1',
  customer: '',
  attachmentIds: ''
})

const confirmLoading = ref(false)
const handleConfirm = () => {
  if (confirmLoading.value) return
  try {
    confirmLoading.value = true
    message.success('处理成功')
  } finally {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  visible.value = false
}

defineExpose({ open })
</script>
