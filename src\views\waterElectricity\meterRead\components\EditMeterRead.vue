<template>
  <a-modal
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新增'}抄表数`"
    width="600px"
    class="common-modal"
    :mask-closable="false"
    :confirm-loading="confirmLoading"
    @ok="handleSave"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ style: { width: '140px' } }">
        <a-form-item label="水电表" name="waterElectriCityTableNum" required>
          <water-electricity-select v-model="selectedWaterElectricityId" @select="handleWaterElectricitySelect" />
        </a-form-item>
        <a-form-item label="抄表归属时间" name="meterReadBelongDate" required>
          <a-date-picker
            v-model:value="formData.meterReadBelongDate"
            placeholder="请选择抄表时间"
            style="width: 100%"
            value-format="YYYY-MM-DD"
          />
        </a-form-item>
        <a-form-item label="表数" name="tableNumber" required>
          <a-input-number
            v-model:value="formData.tableNumber"
            placeholder="请输入表数"
            style="width: 100%"
            :min="0"
            :precision="2"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { addMeterRead, editMeterRead, queryById } from '../apis'
import WaterElectricitySelect from './WaterElectricitySelect.vue'
import { queryById as queryWaterElectricityById } from '@/views/waterElectricity/manage/apis/waterElectricity'

const emit = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()

// 选中的水电表ID
const selectedWaterElectricityId = ref(null)

// 表单默认值
const formDataDefault = reactive({
  id: undefined,
  waterElectriCityTableNum: undefined,
  meterReadBelongDate: undefined,
  tableNumber: undefined
})

const formData = reactive({ ...formDataDefault })

// 表单验证规则
const rules = {
  waterElectriCityTableNum: [{ required: true, message: '请选择水电表', trigger: 'change' }],
  meterReadBelongDate: [{ required: true, message: '请选择抄表时间', trigger: 'change' }],
  tableNumber: [
    { required: true, message: '请输入表数', trigger: 'blur' },
    { type: 'number', min: 0, message: '表数不能为负数', trigger: 'blur' }
  ]
}

/**
 * 处理水电表选择
 */
const handleWaterElectricitySelect = (item) => {
  if (item) {
    formData.waterElectriCityTableNum = item.number
  }
}

/**
 * 保存抄表数信息
 */
const handleSave = async () => {
  await formRef.value.validate()
  confirmLoading.value = true
  try {
    if (formData.id) {
      await editMeterRead(formData)
      message.success('编辑成功')
    } else {
      await addMeterRead(formData)
      message.success('添加成功')
    }
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

/**
 * 取消操作
 */
const handleCancel = () => {
  formRef.value?.resetFields()
  Object.assign(formData, { ...formDataDefault })
  selectedWaterElectricityId.value = null
  visible.value = false
  emit('refresh')
}

/**
 * 打开弹窗
 * @param {Object} record 抄表数记录
 */
const open = async (record) => {
  visible.value = true
  confirmLoading.value = true

  try {
    if (record?.id) {
      const { result } = await queryById({ id: record.id })
      if (result) {
        Object.assign(formData, result)

        if (result.waterElectriCityTableNum) {
          const waterElectricityRes = await queryWaterElectricityById({
            name: result.waterElectriCityTableNum
          })
          if (waterElectricityRes.result) {
            selectedWaterElectricityId.value = waterElectricityRes.result.id
          }
        }
      }
    }
  } finally {
    confirmLoading.value = false
  }
}

defineExpose({ open })
</script>

<style scoped></style>
