<template>
  <div class="water-electricity-select">
    <a-input :value="displayValue" :placeholder="placeholder" readonly @click="handleClick" class="selector-input">
      <template #suffix>
        <i class="a-icon-arrow-down"></i>
        <i v-if="modelValue" class="a-icon-close-solid text-tertiary cursor-pointer" @click.stop="handleClear"></i>
      </template>
    </a-input>

    <a-modal
      v-model:open="visible"
      title="选择水电表"
      width="800px"
      class="common-modal"
      :mask-closable="false"
      @ok="handleConfirm"
      @cancel="handleCancel"
    >
      <div class="mb-4">
        <a-form layout="inline">
          <a-form-item label="">
            <a-input v-model:value="searchParams.name" placeholder="搜索名称/编码" allow-clear>
              <template #prefix>
                <i class="a-icon-search text-primary"></i>
              </template>
            </a-input>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button class="ml-2" @click="handleReset">重置</a-button>
          </a-form-item>
        </a-form>
      </div>

      <!-- 表格区域 -->
      <a-table
        :data-source="list"
        :columns="columns"
        :loading="tableLoading"
        :pagination="pagination"
        row-key="id"
        :row-selection="{
          selectedRowKeys: selectedKeys,
          onChange: onSelectChange,
          type: 'radio'
        }"
        @change="onTableChange"
      ></a-table>
    </a-modal>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import { message } from 'ant-design-vue'
import { getWaterElectricityList } from '@/views/waterElectricity/manage/apis/waterElectricity'

const props = defineProps({
  modelValue: { type: [String, Number], default: null },
  placeholder: { type: String, default: '请选择水电表' },
  params: { type: Object, default: () => ({}) }
})

const emit = defineEmits(['update:modelValue', 'change', 'select'])

// 显示值
const displayValue = ref('')
// 选中的行
const selectedRows = ref([])
// 选中的key
const selectedKeys = ref([])
// 弹窗可见性
const visible = ref(false)

const { list, pagination, tableLoading, onTableFetch } = usePageTable(getWaterElectricityList)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams, ...props.params })
}

// 搜索参数
const searchParams = reactive({
  name: ''
})

// 默认表格列定义
const columns = [
  { title: '编码(表号)', dataIndex: 'number', width: 200 },
  { title: '名称', dataIndex: 'name', width: 160, ellipsis: true },
  { title: '类型', dataIndex: 'type_dictText', width: 120 },
  { title: '属性', dataIndex: 'property_dictText', width: 120 },
  { title: '地址', dataIndex: 'address', width: 160, ellipsis: true },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', width: 160, ellipsis: true }
]

// 监听modelValue变化，更新显示值
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal !== null && newVal !== undefined && newVal !== '') {
      // 清空当前选择
      selectedRows.value = []
      selectedKeys.value = []

      // 查找选中的水电表
      const selectedItem = list.value.find((item) => item.id === newVal)

      if (selectedItem) {
        selectedRows.value = [selectedItem]
        selectedKeys.value = [selectedItem.id]
        displayValue.value = selectedItem.name || selectedItem.number
      } else {
        // 如果找不到对应的信息，显示ID
        displayValue.value = `已选择 ID: ${newVal}`

        // 当列表加载完成后再次尝试查找
        nextTick(() => {
          const foundItem = list.value.find((item) => item.id === newVal)
          if (foundItem) {
            selectedRows.value = [foundItem]
            selectedKeys.value = [foundItem.id]
            displayValue.value = foundItem.name || foundItem.number
          }
        })
      }
    } else {
      displayValue.value = ''
      selectedRows.value = []
      selectedKeys.value = []
    }
  },
  { immediate: true }
)

// 点击输入框
const handleClick = () => {
  visible.value = true
  onTableChange()
}

// 搜索
const handleSearch = () => {
  onTableChange({ pageNo: 1 })
}

// 重置搜索
const handleReset = () => {
  searchParams.name = ''
  onTableChange({ pageNo: 1 })
}

// 清除选择
const handleClear = (e) => {
  e.stopPropagation()
  emit('update:modelValue', null)
  emit('change', null)
  displayValue.value = ''
  selectedRows.value = []
  selectedKeys.value = []
}

// 表格选择变化
const onSelectChange = (keys, rows) => {
  selectedKeys.value = keys
  selectedRows.value = rows
}

// 确认选择
const handleConfirm = () => {
  if (selectedRows.value.length > 0) {
    const selectedItem = selectedRows.value[0]
    emit('update:modelValue', selectedItem.id)
    emit('change', selectedItem.id)
    emit('select', selectedItem)
    displayValue.value = selectedItem.name || selectedItem.number
    visible.value = false
  } else {
    message.warning('请选择一条记录')
  }
}

// 取消选择
const handleCancel = () => {
  if (props.modelValue) {
    const item = list.value.find((item) => item.id === props.modelValue)
    if (item) {
      selectedKeys.value = [item.id]
      selectedRows.value = [item]
    } else {
      selectedKeys.value = []
      selectedRows.value = []
    }
  } else {
    selectedKeys.value = []
    selectedRows.value = []
  }
  visible.value = false
}

// 对外暴露方法
defineExpose({
  open: () => {
    visible.value = true
    onTableChange()
  }
})
</script>

<style scoped>
.water-electricity-select {
  display: inline-block;
  width: 100%;
}

.selector-input {
  cursor: pointer;
  background-color: #fff;
}

.selector-input :deep(.ant-input) {
  cursor: pointer;
}

.selector-input :deep(.ant-input-suffix) {
  display: flex;
  align-items: center;
}

.selector-input :deep(.a-icon-arrow-down) {
  color: rgba(0, 0, 0, 0.25);
  font-size: 12px;
  margin-right: 6px;
  transition: transform 0.3s;
}

.selector-input:hover :deep(.a-icon-arrow-down) {
  color: #000;
}

.selector-input.ant-input-disabled {
  cursor: not-allowed;
}

.selector-input.ant-input-disabled :deep(.ant-input) {
  cursor: not-allowed;
}
</style>
