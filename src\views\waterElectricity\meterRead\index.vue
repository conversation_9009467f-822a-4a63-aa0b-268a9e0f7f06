<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex items-center justify-between mt-[24px] mb-[16px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus mr-1"></i>
          新建
        </a-button>
        <a-button @click="handleImport">
          <i class="a-icon-import-right mr-1"></i>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <i class="a-icon-download mr-1"></i>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button>
            批量操作
            <i class="a-icon-arrow-down ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="batchDelete" @click="handleBatchDelete">删除</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="searchParams.waterElectriCityTableNum"
          placeholder="搜索编码(表号)"
          class="ml-[40px] !w-[280px]"
          @input="handleSearch"
        ></s-input>
        <filter-more
          :params="searchParams"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      :scroll="{ x: 1500, y: tableHeight }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <span class="primary-btn" @click="handleDelete(record)">删除</span>
        </template>
      </template>
    </a-table>

    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('抄表数导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
    ></common-import>
    <edit-meter-read ref="editModalRef" @refresh="onTableChange" />
  </div>
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { getMeterReadList, deleteMeterRead, batchDeleteMeterRead, exportExcel, importExcel } from './apis'
import EditMeterRead from './components/EditMeterRead.vue'

const route = useRoute()

const columnSetRef = ref()
const editModalRef = ref()
const commonImportRef = ref()

const exportLoading = ref(false)

const searchParams = reactive({
  waterElectriCityTableNum: undefined,
  meterReadBelongDate: undefined,
  type: undefined,
  property: undefined,
  tableNumber: undefined,
  address: undefined,
  manageCompany: undefined,
  treeId: undefined,
  source: undefined
})

const pageTitle = computed(() => route.meta.title)

const columns = computed(() => columnSetRef.value?.columns || defaultColumns)

const searchList = reactive([
  { label: '抄表时间', name: 'meterReadBelongDate', type: 'date', placeholder: '请选择抄表时间' },
  {
    label: '类型',
    name: 'type',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_WaterElectriCityTableNum_Type',
    placeholder: '请选择类型'
  },
  {
    label: '属性',
    name: 'property',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_WaterElectriCityTableNum_Property',
    placeholder: '请选择属性'
  },
  { label: '表数', name: 'tableNumber', type: 'input', placeholder: '请输入表数' },
  { label: '水电表', name: 'waterElectriCityTableNum', type: 's-input', placeholder: '请输入水电表' },
  { label: '地址', name: 'address', type: 's-input', placeholder: '请输入地址' },
  { label: '物业管理公司', name: 'manageCompany', type: 'company-select', placeholder: '请选择物业管理公司' },
  { label: '分组', name: 'treeId', type: 'input', placeholder: '请输入分组' },
  { label: '数据来源', name: 'source', type: 's-input', placeholder: '请输入数据来源' }
])

const defaultColumns = [
  { title: '编码(表号)', dataIndex: 'waterElectriCityTableNum', width: 200, fixed: 'left' },
  { title: '抄表时间', dataIndex: 'meterReadBelongDate', width: 120 },
  { title: '类型', dataIndex: 'type_dictText', width: 120 },
  { title: '属性', dataIndex: 'property_dictText', width: 120 },
  { title: '表数', dataIndex: 'tableNumber', width: 120 },
  { title: '地址', dataIndex: 'address', width: 160, ellipsis: true },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', width: 160, ellipsis: true },
  { title: '分组', dataIndex: 'treeId_dictText', width: 160, ellipsis: true },
  { title: '数据来源', dataIndex: 'source', width: 120 },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getMeterReadList)
const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list)

/**
 * 打开新增抄表数弹窗
 */
const handleAdd = () => {
  editModalRef.value.open()
}

/**
 * 打开编辑抄表数弹窗
 * @param {Object} row 选中的行数据
 */
const handleEdit = (row) => {
  editModalRef.value.open(row)
}

/**
 * 打开导入弹窗
 */
const handleImport = () => {
  commonImportRef.value.open()
}

/**
 * 导出抄表数据到Excel
 */
const handleExport = async () => {
  if (exportLoading.value) return
  exportLoading.value = true
  try {
    await exportExcel('抄表数清单.xls', searchParams)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}

/**
 * 删除单条抄表数据
 * @param {Object} record 要删除的记录
 */
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除编码为"${record.number}"的抄表数吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteMeterRead({ id: record.id })
      message.success('删除成功')
      onTableChange()
    }
  })
}

/**
 * 批量删除选中的抄表数据
 */
const handleBatchDelete = () => {
  if (!selectedRowKeys.value.length) {
    message.warning('请选择要删除的数据')
    return
  }
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除选中的${selectedRowKeys.value.length}条数据吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await batchDeleteMeterRead({ ids: selectedRowKeys.value.join(',') })
      message.success('删除成功')
      clearSelection()
      onTableChange()
    }
  })
}

/**
 * 搜索输入防抖处理
 */
let timer
const handleSearch = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

/**
 * 表格变化事件处理
 * @param {Object} params 分页参数
 */
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams })
}

onMounted(() => {
  onTableChange()
})
</script>

<style scoped></style>
